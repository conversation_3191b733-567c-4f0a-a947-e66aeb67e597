/* pages/profile/learning-progress/learning-progress.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

/* 页面头部 */
.page-header {
  padding: 60rpx 32rpx 32rpx 32rpx;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.header-content {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.page-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  display: flex;
  align-items: center;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.edit-btn::after {
  border: none;
}

/* 编辑工具栏 */
.edit-toolbar {
  padding: 0 32rpx 16rpx 32rpx;
  display: flex;
  gap: 16rpx;
}

.toolbar-btn {
  flex: 1;
  padding: 20rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
}

.toolbar-btn::after {
  border: none;
}

.select-all-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.delete-btn {
  background: #ff4757;
  color: #ffffff;
}

/* 加载状态 */
.loading-container {
  padding: 120rpx 32rpx;
}

.loading-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-text {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 空状态 */
.empty-container {
  padding: 120rpx 32rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 48rpx;
  line-height: 1.6;
}

.empty-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.empty-btn::after {
  border: none;
}

/* 进度列表 */
.progress-list {
  padding: 0 32rpx 100rpx 32rpx;
}

.progress-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.progress-item.edit-mode {
  padding-left: 16rpx;
}

.progress-content {
  flex: 1;
  margin-left: 16rpx;
}

.progress-item:not(.edit-mode) .progress-content {
  margin-left: 0;
}

/* 选择框 */
.select-checkbox {
  padding: 8rpx;
  margin-right: 16rpx;
}

.checkbox {
  width: 44rpx;
  height: 44rpx;
  border: 3rpx solid #cbd5e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  transition: all 0.3s ease;
  position: relative;
}

.checkbox.checked {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  border-color: #38a169;
  box-shadow: 0 4rpx 12rpx rgba(72, 187, 120, 0.3);
}

.checkbox.disabled {
  background: #f7fafc;
  border-color: #e2e8f0;
  opacity: 0.6;
}

.check-icon {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
}

.disabled-icon {
  color: #a0aec0;
  font-size: 24rpx;
  font-weight: bold;
}

/* 进度头部 */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.library-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.library-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 6rpx;
  line-height: 1.3;
}

.mode-tag {
  display: inline-block;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 500;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  align-self: flex-start;
}

.progress-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.progress-percent {
  font-size: 36rpx;
  font-weight: bold;
  color: #4facfe;
  margin-bottom: 4rpx;
}

.last-study-time {
  font-size: 22rpx;
  color: #a0aec0;
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e2e8f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 进度详情 */
.progress-details {
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 500;
}

.detail-text {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 500;
  margin-top: 5rpx;
  display: block;
}

/* 操作按钮 */
.progress-actions {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 0;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-btn::after {
  border: none;
}

.continue-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.3);
}

.view-btn {
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border: 2rpx solid #4facfe;
}

.clear-btn {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
  border: 2rpx solid #ff4757;
}

.clear-btn:disabled {
  background: #f7fafc;
  color: #cbd5e0;
  border-color: #cbd5e0;
}

/* 按钮点击效果 */
.action-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 禁用状态的按钮 */
.action-btn[disabled] {
  opacity: 0.5;
  background: rgba(200, 200, 200, 0.2) !important;
  color: #999 !important;
  border-color: rgba(200, 200, 200, 0.3) !important;
}

/* 响应式调整 */
@media (max-height: 800px) {
  .progress-item {
    padding: 20rpx;
    margin-bottom: 12rpx;
  }
  
  .library-name {
    font-size: 30rpx;
  }
  
  .progress-percent {
    font-size: 32rpx;
  }
  
  .action-btn {
    padding: 14rpx 10rpx;
    font-size: 24rpx;
  }
} 