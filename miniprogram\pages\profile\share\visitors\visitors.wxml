<view class="container">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-title">访问者管理</view>
    <view class="nav-actions">
      <text class="nav-btn" bindtap="loadVisitorsData">刷新</text>
      <text class="nav-btn" bindtap="exportVisitors">导出</text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-number">{{testStats.totalVisitors}}</text>
          <text class="stat-label">总访问者</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{testStats.totalTests}}</text>
          <text class="stat-label">总测试次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{testStats.averageScore}}</text>
          <text class="stat-label">平均分数</text>
        </view>
      </view>
      
      <view class="stats-row" wx:if="{{testStats.totalTests > 0}}">
        <view class="stat-item">
          <text class="stat-number">{{testStats.passRate}}%</text>
          <text class="stat-label">及格率</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{testStats.highestScore}}</text>
          <text class="stat-label">最高分</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{testStats.lowestScore}}</text>
          <text class="stat-label">最低分</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 测试基本信息 -->
  <view class="test-info-section" wx:if="{{shareTestData}}">
    <view class="info-card">
      <view class="info-header">
        <text class="info-icon">{{shareTestData.testMode === 'en_to_cn' ? '🇨🇳' : shareTestData.testMode === 'cn_to_en' ? '🇺🇸' : shareTestData.testMode === 'dictation' ? '🎧' : shareTestData.testMode === 'elimination' ? '🎮' : '📝'}}</text>
        <text class="info-title">{{getModeText(shareTestData.testMode)}}</text>
      </view>
      <view class="info-details">
        <text class="info-item">测试ID: {{shareId}}</text>
        <text class="info-item">单词数量: {{shareTestData.words ? shareTestData.words.length : 0}}个</text>
        <text class="info-item">创建时间: {{formatTime(shareTestData.createTime)}}</text>
      </view>
    </view>
  </view>

  <!-- 访问者列表 -->
  <view class="visitors-section">
    <view class="section-title">访问者列表 ({{visitors.length}})</view>
    
    <view wx:if="{{visitors.length === 0}}" class="empty-state">
      <text class="empty-icon">👥</text>
      <text class="empty-text">暂无访问者</text>
      <text class="empty-desc">分享测试链接后，访问者信息将在此显示</text>
    </view>

    <view wx:else class="visitors-list">
      <view class="visitor-item" wx:for="{{visitors}}" wx:key="openid" data-index="{{index}}">
        <!-- 用户头像和基本信息 -->
        <view class="visitor-header">
          <view class="visitor-avatar">
            <image wx:if="{{item.avatarUrl}}" src="{{item.avatarUrl}}" mode="aspectFill"></image>
            <text wx:else class="avatar-placeholder">{{item.avatarText || '匿'}}</text>
          </view>
          <view class="visitor-info">
            <view class="visitor-name">
              <text class="name-text">{{item.nickName || '匿名用户'}}</text>
              <text wx:if="{{item.remark}}" class="remark-text">({{item.remark}})</text>
            </view>
            <view class="visitor-meta">
              <text class="meta-text">首次访问: {{formatTime(item.visitTime)}}</text>
              <text wx:if="{{item.lastTestTime}}" class="meta-text">最后测试: {{formatTime(item.lastTestTime)}}</text>
            </view>
          </view>
        </view>

        <!-- 测试统计 -->
        <view class="visitor-stats">
          <view class="stat-card">
            <text class="stat-number">{{item.testCount || 0}}</text>
            <text class="stat-label">测试次数</text>
          </view>
          <view class="stat-card">
            <text class="stat-number">{{item.averageScore || 0}}</text>
            <text class="stat-label">平均分</text>
          </view>
          <view class="stat-card">
            <text class="stat-number">{{item.highestScore || 0}}</text>
            <text class="stat-label">最高分</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="visitor-actions">
          <button 
            class="action-btn secondary" 
            data-index="{{index}}" 
            bindtap="editRemark"
          >
            {{item.remark ? '修改备注' : '添加备注'}}
          </button>
          <button 
            wx:if="{{item.testCount > 0}}"
            class="action-btn primary" 
            data-index="{{index}}" 
            bindtap="viewTestDetail"
          >
            查看详情
          </button>
          <button 
            class="action-btn danger" 
            data-index="{{index}}" 
            bindtap="deleteVisitor"
          >
            删除
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 备注弹窗 -->
  <view wx:if="{{showRemarkModal}}" class="modal-overlay" bindtap="cancelRemark">
    <view class="modal-container" catchtap="">
      <view class="modal-header">
        <text class="modal-title">{{currentVisitor.remark ? '修改' : '添加'}}备注</text>
        <view class="modal-close" bindtap="cancelRemark">×</view>
      </view>
      
      <view class="modal-body">
        <view class="user-info">
          <text class="user-name">{{currentVisitor.nickName || '匿名用户'}}</text>
        </view>
        <textarea 
          class="remark-input"
          placeholder="请输入备注信息..."
          value="{{remarkText}}"
          bindinput="onRemarkInput"
          maxlength="50"
          auto-focus
        ></textarea>
        <view class="input-tip">
          <text class="tip-text">{{remarkText.length}}/50</text>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn cancel" bindtap="cancelRemark">取消</button>
        <button class="modal-btn confirm" bindtap="saveRemark">保存</button>
      </view>
    </view>
  </view>

  <!-- 测试详情弹窗 -->
  <view wx:if="{{showTestDetailModal}}" class="detail-modal-overlay" bindtap="closeTestDetailModal">
    <view class="detail-modal-container" catchtap="">
      <view class="detail-modal-header">
        <view class="detail-title">
          <text class="detail-user-name">{{currentVisitor.nickName || '匿名用户'}}</text>
          <text class="detail-subtitle">测试详情</text>
        </view>
        <view class="detail-close" bindtap="closeTestDetailModal">×</view>
      </view>
      
      <view class="detail-modal-body">
        <!-- 用户总体统计 -->
        <view class="detail-stats">
          <view class="detail-stat-item">
            <text class="detail-stat-number">{{currentVisitor.testCount}}</text>
            <text class="detail-stat-label">测试次数</text>
          </view>
          <view class="detail-stat-item">
            <text class="detail-stat-number">{{currentVisitor.averageScore}}</text>
            <text class="detail-stat-label">平均分</text>
          </view>
          <view class="detail-stat-item">
            <text class="detail-stat-number">{{currentVisitor.highestScore}}</text>
            <text class="detail-stat-label">最高分</text>
          </view>
        </view>

        <!-- 测试记录列表 -->
        <view class="test-records">
          <view class="records-title">历史测试记录</view>
          <view class="record-item" wx:for="{{selectedVisitorResults}}" wx:key="timestamp">
            <view class="record-header">
              <text class="record-score {{item.score >= 60 ? 'pass' : 'fail'}}">{{item.score || 0}}分</text>
              <text class="record-time">{{formatTime(item.timestamp)}}</text>
            </view>
            <view class="record-details" wx:if="{{item.details}}">
              <text class="record-detail">正确: {{item.details.correct || 0}}题</text>
              <text class="record-detail">错误: {{item.details.wrong || 0}}题</text>
              <text class="record-detail">用时: {{item.details.duration || 0}}秒</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view> 