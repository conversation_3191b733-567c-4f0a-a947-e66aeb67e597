<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">学生管理</view>
    <view class="subtitle">班级学情跟踪与分析</view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-container">
    <view class="stats-card">
      <view class="stat-item">
        <view class="stat-number">{{stats.totalStudents}}</view>
        <view class="stat-label">学生总数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.maxScore}}</view>
        <view class="stat-label">最高分</view>
      </view>
      <view class="stat-item">
        <view class="stat-number green">{{stats.improvedCount}}</view>
        <view class="stat-label">进步人数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number red">{{stats.declinedCount}}</view>
        <view class="stat-label">退步人数</view>
      </view>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar">
    <button class="add-btn" bindtap="showAddStudentDialog">+ 添加学生</button>
    <button class="manage-btn {{isManageMode ? 'active' : ''}}" bindtap="toggleManageMode">
      {{isManageMode ? '完成管理' : '管理学生'}}
    </button>
  </view>

  <!-- 管理模式下的批量操作 -->
  <view class="batch-actions" wx:if="{{isManageMode && selectedStudents.length > 0}}">
    <view class="batch-info">已选择 {{selectedStudents.length}} 个学生</view>
    <button class="batch-delete-btn" bindtap="batchDeleteStudents">删除选中</button>
  </view>

  <!-- 学生列表 -->
  <view class="students-container" wx:if="{{!loading}}">
    <view class="student-card" wx:for="{{students}}" wx:key="_id">
      <view class="student-content">
        <!-- 管理模式下的复选框 -->
        <view class="checkbox-container" wx:if="{{isManageMode}}" bindtap="toggleStudentSelection" data-id="{{item._id}}">
          <view class="checkbox {{item.selected ? 'checked' : ''}}">
            <text class="checkmark" wx:if="{{item.selected}}">✓</text>
          </view>
        </view>

        <view class="student-header" data-id="{{item._id}}" bindtap="{{isManageMode ? 'toggleStudentSelection' : 'viewStudentDetail'}}">
          <view class="student-info">
            <view class="student-name">{{item.name}}</view>
            <view class="student-details">
              <text class="student-gender">{{item.gender}}</text>
              <text class="student-school">{{item.school}}</text>
            </view>
          </view>
          <view class="student-stats">
            <view class="score-info">
              <view class="recent-score">最新成绩: {{item.recentScoreDisplay}}</view>
              <view class="avg-score">上次成绩: {{item.previousScoreDisplay}}</view>
              <view class="score-count">{{item.scoreCount}}次考试</view>
            </view>
            <view class="trend-indicator">
              <text class="trend-icon {{item.trend}}">
                {{item.trend === 'up' ? '📈' : item.trend === 'down' ? '📉' : '➖'}}
              </text>
            </view>
          </view>
        </view>
      </view>

      <view class="student-actions" wx:if="{{!isManageMode}}">
        <button
          class="action-btn primary"
          data-student="{{item}}"
          bindtap="showAddScoreDialog"
        >添加成绩</button>
        <button
          class="action-btn secondary"
          data-id="{{item._id}}"
          bindtap="viewStudentDetail"
        >查看详情</button>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{students.length === 0}}">
      <view class="empty-icon">👨‍🎓</view>
      <view class="empty-text">暂无学生数据</view>
      <view class="empty-desc">点击上方"添加学生"按钮开始管理学生信息</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>
</view>

<!-- 添加学生对话框 -->
<view class="modal-overlay" wx:if="{{showAddDialog}}" bindtap="onModalOverlayClick">
  <view class="modal-content" catchtap="onModalContentClick">
    <view class="modal-header">
      <view class="modal-title">添加学生</view>
      <view class="modal-close" catchtap="hideAddStudentDialog">×</view>
    </view>

    <view class="modal-body">
      <view class="form-group">
        <view class="form-label">学生姓名 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            placeholder="请输入学生姓名"
            placeholder-class="input-placeholder"
            value="{{newStudent.name}}"
            data-field="name"
            bindinput="onStudentInput"
            bindfocus="onInputFocus"
            bindblur="onInputBlur"
            binderror="onInputError"
            confirm-type="next"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">性别 *</view>
        <view class="picker-container" catchtap="">
          <picker
            range="{{genderOptions}}"
            value="{{newStudent.genderIndex}}"
            data-field="gender"
            bindchange="onStudentPickerChange"
          >
            <view class="form-input picker-input">
              <text class="{{newStudent.gender ? '' : 'placeholder-text'}}">
                {{newStudent.gender || '请选择性别'}}
              </text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">学校 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            placeholder="请输入学校名称"
            placeholder-class="input-placeholder"
            value="{{newStudent.school}}"
            data-field="school"
            bindinput="onStudentInput"
            bindfocus="onInputFocus"
            bindblur="onInputBlur"
            binderror="onInputError"
            confirm-type="done"
          />
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideAddStudentDialog">取消</button>
      <button class="modal-btn confirm" bindtap="addStudent">添加</button>
    </view>
  </view>
</view>

<!-- 添加成绩对话框 -->
<view class="modal-overlay" wx:if="{{showScoreDialog}}" bindtap="onScoreModalOverlayClick">
  <view class="modal-content" catchtap="onScoreModalContentClick">
    <view class="modal-header">
      <view class="modal-title">添加成绩 - {{selectedStudent.name}}</view>
      <view class="modal-close" catchtap="hideAddScoreDialog">×</view>
    </view>

    <view class="modal-body">
      <view class="form-group">
        <view class="form-label">考试名称 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            placeholder="如：期中考试、月考"
            value="{{newScore.examName}}"
            data-field="examName"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">得分 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            type="digit"
            placeholder="请输入得分"
            value="{{newScore.score}}"
            data-field="score"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">总分 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            type="digit"
            placeholder="请输入总分"
            value="{{newScore.totalScore}}"
            data-field="totalScore"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">年级 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            placeholder="如：高一、高二、高三"
            value="{{newScore.grade}}"
            data-field="grade"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">学期 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            placeholder="如：上学期、下学期"
            value="{{newScore.semester}}"
            data-field="semester"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">考试日期 *</view>
        <view class="picker-container" catchtap="">
          <picker
            mode="date"
            value="{{newScore.examDate}}"
            data-field="examDate"
            bindchange="onScoreInput"
          >
            <view class="form-input picker">{{newScore.examDate || '选择日期'}}</view>
          </picker>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideAddScoreDialog">取消</button>
      <button class="modal-btn confirm" bindtap="addScore">添加</button>
    </view>
  </view>
</view>
