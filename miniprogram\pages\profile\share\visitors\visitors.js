Page({
  data: {
    shareId: '',
    shareTestData: null,
    visitors: [],
    showRemarkModal: false,
    showTestDetailModal: false,
    currentVisitor: null,
    currentVisitorIndex: -1,
    remarkText: '',
    selectedVisitorResults: [],
    
    // 测试统计
    testStats: {
      totalVisitors: 0,
      totalTests: 0,
      averageScore: 0,
      passRate: 0,
      highestScore: 0,
      lowestScore: 0
    }
  },

  onLoad(options) {
    const { shareId } = options;
    if (!shareId) {
      wx.showToast({ title: '参数错误', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ shareId });
    this.loadVisitorsData();
  },

  // 加载访问者数据
  loadVisitorsData() {
    wx.showLoading({ title: '加载中...' });
    
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};
      const shareTestData = shareTests[this.data.shareId];
      
      if (!shareTestData) {
        wx.hideLoading();
        wx.showToast({ title: '分享测试不存在', icon: 'error' });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 获取访问者数据，同时获取他们的测试结果
      const visitors = this.processVisitorsData(shareTestData);
      
      // 计算统计数据
      const testStats = this.calculateTestStats(visitors);
      
      this.setData({
        shareTestData,
        visitors,
        testStats
      });

      wx.hideLoading();

    } catch (error) {
      wx.hideLoading();
      console.error('加载访问者数据失败:', error);
      wx.showToast({ title: '数据加载失败', icon: 'error' });
    }
  },

  // 处理访问者数据，获取详细的测试结果
  processVisitorsData(shareTestData) {
    const visitors = shareTestData.visitors || [];
    const results = shareTestData.results || [];
    
    // 为每个访问者计算详细统计
    return visitors.map(visitor => {
      const visitorResults = results.filter(result => 
        result.userOpenid === visitor.openid || 
        result.userId === visitor.openid
      );
      
      // 计算该访问者的统计数据
      const testCount = visitorResults.length;
      const scores = visitorResults.map(r => r.score || 0);
      const totalScore = scores.reduce((sum, score) => sum + score, 0);
      const averageScore = testCount > 0 ? Math.round(totalScore / testCount) : 0;
      const highestScore = testCount > 0 ? Math.max(...scores) : 0;
      const lastTestTime = testCount > 0 ? Math.max(...visitorResults.map(r => r.timestamp || 0)) : 0;
      
      return {
        ...visitor,
        testCount: testCount,
        totalScore: totalScore,
        averageScore: averageScore,
        highestScore: highestScore,
        lastTestTime: lastTestTime,
        avatarText: (visitor.nickName || '匿名').charAt(0),
        testResults: visitorResults.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
      };
    }).sort((a, b) => (b.lastTestTime || b.visitTime || 0) - (a.lastTestTime || a.visitTime || 0));
  },

  // 计算测试统计
  calculateTestStats(visitors) {
    const stats = {
      totalVisitors: visitors.length,
      totalTests: 0,
      averageScore: 0,
      passRate: 0,
      highestScore: 0,
      lowestScore: 0
    };

    if (visitors.length === 0) {
      return stats;
    }

    let totalScore = 0;
    let testCount = 0;
    let passCount = 0;
    const allScores = [];

    visitors.forEach(visitor => {
      const visitorTestCount = visitor.testCount || 0;
      const visitorTotalScore = visitor.totalScore || 0;
      const visitorHighestScore = visitor.highestScore || 0;
      
      testCount += visitorTestCount;
      totalScore += visitorTotalScore;
      
      if (visitorHighestScore >= 60) { // 假设60分为及格线
        passCount++;
      }
      
      if (visitor.testResults) {
        visitor.testResults.forEach(result => {
          allScores.push(result.score || 0);
        });
      }
    });

    stats.totalTests = testCount;
    stats.averageScore = testCount > 0 ? Math.round(totalScore / testCount) : 0;
    stats.passRate = visitors.length > 0 ? Math.round((passCount / visitors.length) * 100) : 0;
    
    if (allScores.length > 0) {
      stats.highestScore = Math.max(...allScores);
      stats.lowestScore = Math.min(...allScores);
    }

    return stats;
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '未知';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    // 小于1分钟
    if (diff < 60000) return '刚刚';
    
    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    
    // 小于1天
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }
    
    // 小于7天
    if (diff < 604800000) {
      return Math.floor(diff / 86400000) + '天前';
    }
    
    // 超过7天，显示具体日期
    return date.getFullYear() + '/' + 
           String(date.getMonth() + 1).padStart(2, '0') + '/' + 
           String(date.getDate()).padStart(2, '0') + ' ' +
           String(date.getHours()).padStart(2, '0') + ':' +
           String(date.getMinutes()).padStart(2, '0');
  },

  // 获取模式文本
  getModeText(mode) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写',
      'elimination': '消消乐'
    };
    return modeMap[mode] || mode || '未知模式';
  },

  // 添加/编辑备注
  editRemark(e) {
    const index = e.currentTarget.dataset.index;
    const visitor = this.data.visitors[index];
    
    this.setData({
      showRemarkModal: true,
      currentVisitor: visitor,
      currentVisitorIndex: index,
      remarkText: visitor.remark || ''
    });
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({
      remarkText: e.detail.value
    });
  },

  // 保存备注
  saveRemark() {
    const { currentVisitorIndex, remarkText, shareId } = this.data;
    
    if (currentVisitorIndex < 0) return;
    
    try {
      // 更新本地数据
      const visitors = [...this.data.visitors];
      visitors[currentVisitorIndex].remark = remarkText.trim();
      
      // 更新存储
      const shareTests = wx.getStorageSync('shareTests') || {};
      const shareTestData = shareTests[shareId];
      if (shareTestData) {
        shareTestData.visitors = visitors.map(v => ({
          openid: v.openid,
          nickName: v.nickName,
          avatarUrl: v.avatarUrl,
          visitTime: v.visitTime,
          remark: v.remark
        }));
        shareTests[shareId] = shareTestData;
        wx.setStorageSync('shareTests', shareTests);
      }
      
      this.setData({
        visitors,
        showRemarkModal: false,
        currentVisitor: null,
        currentVisitorIndex: -1,
        remarkText: ''
      });
      
      wx.showToast({ title: '备注已保存', icon: 'success' });
      
    } catch (error) {
      console.error('保存备注失败:', error);
      wx.showToast({ title: '保存失败', icon: 'error' });
    }
  },

  // 取消备注编辑
  cancelRemark() {
    this.setData({
      showRemarkModal: false,
      currentVisitor: null,
      currentVisitorIndex: -1,
      remarkText: ''
    });
  },

  // 查看测试详情
  viewTestDetail(e) {
    const index = e.currentTarget.dataset.index;
    const visitor = this.data.visitors[index];
    
    if (!visitor.testResults || visitor.testResults.length === 0) {
      wx.showToast({ title: '该用户暂无测试记录', icon: 'none' });
      return;
    }
    
    this.setData({
      showTestDetailModal: true,
      currentVisitor: visitor,
      selectedVisitorResults: visitor.testResults
    });
  },

  // 关闭测试详情弹窗
  closeTestDetailModal() {
    this.setData({
      showTestDetailModal: false,
      currentVisitor: null,
      selectedVisitorResults: []
    });
  },

  // 删除访问者
  deleteVisitor(e) {
    const index = e.currentTarget.dataset.index;
    const visitor = this.data.visitors[index];
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除访问者"${visitor.nickName || '匿名用户'}"的记录吗？\n\n这将同时删除该用户的所有测试记录，且无法恢复。`,
      confirmText: '确认删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteVisitor(index, visitor);
        }
      }
    });
  },

  // 执行删除访问者
  doDeleteVisitor(index, visitor) {
    try {
      const { shareId } = this.data;
      const shareTests = wx.getStorageSync('shareTests') || {};
      const shareTestData = shareTests[shareId];
      
      if (shareTestData) {
        // 删除访问者记录
        const updatedVisitors = shareTestData.visitors.filter((v, i) => 
          !(v.openid === visitor.openid)
        );
        
        // 删除该用户的测试结果
        const updatedResults = (shareTestData.results || []).filter(result => 
          result.userOpenid !== visitor.openid && result.userId !== visitor.openid
        );
        
        shareTestData.visitors = updatedVisitors;
        shareTestData.results = updatedResults;
        shareTests[shareId] = shareTestData;
        wx.setStorageSync('shareTests', shareTests);
      }
      
      wx.showToast({ title: '删除成功', icon: 'success' });
      
      // 重新加载数据
      this.loadVisitorsData();
      
    } catch (error) {
      console.error('删除访问者失败:', error);
      wx.showToast({ title: '删除失败', icon: 'error' });
    }
  },

  // 导出访问者数据
  exportVisitors() {
    const { visitors, shareTestData } = this.data;
    
    if (visitors.length === 0) {
      wx.showToast({ title: '暂无访问者数据', icon: 'none' });
      return;
    }
    
    // 生成导出文本
    let exportText = `📊 ${this.getModeText(shareTestData.testMode)} - 访问者统计报告\n`;
    exportText += `📅 导出时间：${this.formatTime(Date.now())}\n`;
    exportText += `🔗 测试ID：${this.data.shareId}\n\n`;
    
    exportText += `📈 总体统计：\n`;
    exportText += `• 总访问者：${this.data.testStats.totalVisitors}人\n`;
    exportText += `• 总测试次数：${this.data.testStats.totalTests}次\n`;
    exportText += `• 平均分数：${this.data.testStats.averageScore}分\n`;
    exportText += `• 及格率：${this.data.testStats.passRate}%\n\n`;
    
    exportText += `👥 访问者详情：\n`;
    visitors.forEach((visitor, index) => {
      exportText += `${index + 1}. ${visitor.nickName || '匿名用户'}\n`;
      if (visitor.remark) {
        exportText += `   备注：${visitor.remark}\n`;
      }
      exportText += `   测试次数：${visitor.testCount}次\n`;
      exportText += `   最高分：${visitor.highestScore}分\n`;
      exportText += `   平均分：${visitor.averageScore}分\n`;
      exportText += `   最后测试：${this.formatTime(visitor.lastTestTime)}\n\n`;
    });
    
    wx.setClipboardData({
      data: exportText,
      success: () => {
        wx.showModal({
          title: '导出成功',
          content: '访问者统计报告已复制到剪贴板，可以粘贴到其他应用中保存或分享。',
          showCancel: false,
          confirmText: '知道了'
        });
      },
      fail: () => {
        wx.showToast({ title: '导出失败', icon: 'error' });
      }
    });
  },

  // 刷新数据
  onPullDownRefresh() {
    this.loadVisitorsData();
    wx.stopPullDownRefresh();
  },

  // 返回上一页
  onBack() {
    wx.navigateBack();
  }
}); 