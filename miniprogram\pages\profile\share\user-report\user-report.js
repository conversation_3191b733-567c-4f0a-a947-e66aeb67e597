Page({

  /**
   * 页面的初始数据
   */
  data: {
    userOpenid: '',
    userName: '',
    userAvatar: '',
    shareId: '', // 添加shareId字段
    testResults: [], // 该用户的所有测试结果
    statistics: {
      totalTests: 0,
      totalScore: 0,
      averageScore: 0,
      totalTime: 0,
      averageTime: 0,
      accuracy: 0,
      bestScore: 0,
      recentTests: []
    },
    loading: true,
    chartData: [], // 用于展示分数趋势图的数据

    // 详细测试结果相关
    showDetailModal: false,
    currentTestDetail: null,
    selectedMistakes: [], // 选中的错词
    allMistakes: [], // 当前测试的所有错词

    // 重新测试相关
    showRetestModal: false,
    selectedTestMode: 'en_to_cn',
    selectedPracticeMode: 'test',
    selectedShareOption: 'self',
    selectedTestModeText: '英译汉',
    selectedPracticeModeText: '测试'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { openid, nickName, shareId, avatarUrl } = options;

    this.setData({
      userOpenid: openid,
      userName: decodeURIComponent(nickName || '匿名用户'),
      shareId: shareId || '',
      userAvatar: decodeURIComponent(avatarUrl || '') || '/assets/icons/profile.png'
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: `${this.data.userName}的报告`
    });

    this.loadUserReport();
  },

  /**
   * 加载用户报告数据
   */
  async loadUserReport() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      const currentUserOpenid = currentUser.openid;

      // 从云端获取用户测试结果
      const cloudResult = await wx.cloud.callFunction({
        name: 'getUserTestResults',
        data: {
          shareId: this.data.shareId,
          participantOpenid: this.data.userOpenid,
          // 不传递creatorOpenid，让云函数根据shareId查询
          includeAllResults: true // 包含所有结果，不限制创建者
        }
      });

      let userResults = [];
      if (cloudResult.result && cloudResult.result.success) {
        userResults = cloudResult.result.data || [];
      } else {
        console.log('云端获取用户测试结果失败，尝试本地数据');
        // 回退到本地数据
        const testResults = wx.getStorageSync('testResults') || [];

        // 如果有shareId，按shareId过滤；否则按参与者openid过滤
        if (this.data.shareId) {
          userResults = testResults.filter(result =>
            result.shareMode === 'share' &&
            result.shareId === this.data.shareId &&
            result.participantOpenid === this.data.userOpenid &&
            !result.isDeleted
          );
        } else {
          userResults = testResults.filter(result =>
            result.shareMode === 'share' &&
            result.participantOpenid === this.data.userOpenid &&
            !result.isDeleted
          );
        }
      }

      console.log('获取到的用户测试结果:', userResults);
      console.log('用户参数:', {
        userOpenid: this.data.userOpenid,
        userName: this.data.userName,
        shareId: this.data.shareId
      });

      // 按时间排序
      userResults.sort((a, b) => (b.timestamp || b.submitTime || 0) - (a.timestamp || a.submitTime || 0));

      // 为每个结果添加预计算的文本
      userResults.forEach(result => {
        result.testModeText = this.getTestModeText(result.testMode);
        result.timeText = this.formatTime(result.timestamp);
        result.durationText = this.formatDuration(result.duration);
      });

      // 计算统计数据
      const statistics = this.calculateStatistics(userResults);
      console.log('计算的统计数据:', statistics);

      // 设置用户头像 - 优先使用传递的头像，然后从测试结果中获取
      let userAvatar = this.data.userAvatar;
      if (!userAvatar || userAvatar === '/assets/icons/profile.png') {
        if (userResults.length > 0 && userResults[0].participantInfo) {
          userAvatar = userResults[0].participantInfo.avatarUrl || userAvatar;
        }
      }

      this.setData({
        testResults: userResults,
        statistics: statistics,
        userAvatar: userAvatar,
        loading: false
      });

      wx.hideLoading();

    } catch (error) {
      wx.hideLoading();
      console.error('加载用户报告失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  /**
   * 计算统计数据
   */
  calculateStatistics(results) {
    if (results.length === 0) {
      return {
        totalTests: 0,
        totalScore: 0,
        averageScore: 0,
        totalTime: 0,
        averageTime: 0,
        accuracy: 0,
        bestScore: 0,
        recentTests: []
      };
    }

    // 计算总分和平均分
    const totalScore = results.reduce((sum, result) => sum + (result.score || 0), 0);
    const averageScore = Math.round(totalScore / results.length);

    // 计算总时长和平均时长
    const totalTime = results.reduce((sum, result) => sum + (result.duration || 0), 0);
    const averageTime = Math.round(totalTime / results.length);

    // 计算平均准确率
    const totalAccuracy = results.reduce((sum, result) => sum + (parseFloat(result.accuracy) || 0), 0);
    const accuracy = Math.round(totalAccuracy / results.length * 10) / 10;

    // 最高分
    const bestScore = Math.max(...results.map(result => result.score || 0));

    // 最近的测试记录（取前5个）
    const recentTests = results.slice(0, 5).map(result => ({
      testMode: result.testMode,
      score: result.score || 0,
      accuracy: result.accuracy || '0.0',
      duration: result.duration || 0,
      timestamp: result.timestamp || 0,
      libraryName: result.libraryName || '未知词库'
    }));

    return {
      totalTests: results.length,
      totalScore: totalScore,
      averageScore: averageScore,
      totalTime: totalTime,
      averageTime: averageTime,
      accuracy: accuracy,
      bestScore: bestScore,
      recentTests: recentTests
    };
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知时间';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 格式化持续时间
   */
  formatDuration(milliseconds) {
    if (!milliseconds) return '0秒';
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      return `${remainingSeconds}秒`;
    }
  },

  /**
   * 获取测试模式文本
   */
  getTestModeText(testMode) {
    switch (testMode) {
      case 'en_to_cn':
        return '英译汉';
      case 'cn_to_en':
        return '汉译英';
      case 'dictation':
        return '听写';
      case 'elimination':
        return '消消乐';
      default:
        return '未知模式';
    }
  },

  /**
   * 显示分享选项
   */
  showShareOptions(options) {
    const { title, content, shareId, shareData } = options;
    
    wx.showActionSheet({
      itemList: ['复制链接', '分享到微信', '查看管理'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 复制链接
            this.copyShareLink(shareData);
            break;
          case 1:
            // 分享到微信
            this.shareToWeChat(shareData);
            break;
          case 2:
            // 查看管理
            this.goToShareManagement(shareId);
            break;
        }
      }
    });
  },

  /**
   * 复制分享链接
   */
  copyShareLink(shareData) {
    const shareUrl = this.getSharePath(shareData.testMode, shareData.shareId);
    wx.setClipboardData({
      data: shareUrl,
      success: () => {
        wx.showToast({ title: '链接已复制', icon: 'success' });
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  /**
   * 分享到微信
   */
  shareToWeChat(shareData) {
    const { testMode, testName, shareId } = shareData;
    
    // 获取测试模式的emoji图标
    const modeEmojis = {
      'en_to_cn': '🇨🇳',
      'cn_to_en': '🇺🇸',
      'dictation': '🎧',
      'elimination': '🎮'
    };
    
    const emoji = modeEmojis[testMode] || '📝';
    
    // 更新当前分享数据
    const shareTitle = `${emoji} ${testName}`;
    const sharePath = this.getSharePath(testMode, shareId);
    
    // 设置当前分享数据
    this.setData({
      currentShareData: {
        title: shareTitle,
        path: sharePath,
        imageUrl: '/assets/icons/logo.png'
      }
    });

    // 主动触发分享
    wx.shareAppMessage({
      title: shareTitle,
      path: sharePath,
      imageUrl: '/assets/icons/logo.png',
      success: () => {
        // 显示分享成功弹窗，提供返回和查看分享页选项
        this.showShareSuccessModal();
      },
      fail: () => {
        wx.showToast({ title: '分享取消', icon: 'none' });
      }
    });
  },

  /**
   * 获取分享路径
   */
  getSharePath(testMode, shareId) {
    if (testMode === 'elimination') {
      return `pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom`;
    } else if (testMode === 'dictation') {
      return `pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
    } else if (testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en') {
      // 短语测试需要传递isPhrase参数
      const actualTestMode = testMode === 'phrase_en2zh' ? 'en_to_cn' : 'cn_to_en';
      return `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${actualTestMode}&isPhrase=true`;
    } else {
      return `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}`;
    }
  },

  /**
   * 跳转到分享管理页面
   */
  goToShareManagement(shareId) {
    wx.navigateTo({
      url: '/pages/profile/share/share',
      success: () => {
        console.log('跳转到我的分享页面成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({ 
          title: '跳转失败', 
          icon: 'none'
        });
      }
    });
  },

  /**
   * 导出报告
   */
  exportReport() {
    wx.showActionSheet({
      itemList: ['保存为图片', '复制数据', '分享报告'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.saveAsImage();
            break;
          case 1:
            this.copyData();
            break;
          case 2:
            this.shareReport();
            break;
        }
      }
    });
  },

  /**
   * 保存为图片
   */
  saveAsImage() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 复制数据
   */
  copyData() {
    const { userName, statistics } = this.data;
    const text = `${userName}的测试报告
总测试次数：${statistics.totalTests}次
平均分数：${statistics.averageScore}分
最高分数：${statistics.bestScore}分
平均准确率：${statistics.accuracy}%
平均用时：${this.formatDuration(statistics.averageTime)}

生成时间：${new Date().toLocaleString()}`;

    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '数据已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 分享报告
   */
  shareReport() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 如果有当前分享数据，使用它
    if (this.data.currentShareData) {
      return this.data.currentShareData;
    }
    
    // 否则使用默认的报告分享
    const { userName, statistics } = this.data;
    return {
      title: `${userName}的测试报告 - 平均分${statistics.averageScore}分`,
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡，用于弹窗内容点击
  },

  /**
   * 查看测试详情
   */
  viewTestDetail(e) {
    const index = e.currentTarget.dataset.index;
    const testResult = this.data.testResults[index];
    
    if (!testResult) {
      wx.showToast({ title: '测试数据不存在', icon: 'error' });
      return;
    }

    // 获取测试的错词数据
    this.loadTestMistakes(testResult);
  },

  /**
   * 加载测试的错词数据
   */
  loadTestMistakes(testResult) {
    // 所有测试模式统一使用mistakes字段存储错词数据
    const mistakes = (testResult.mistakes || []).map((mistake, index) => {
      // 处理word字段，可能是对象或字符串
      let wordText = '';
      if (typeof mistake.word === 'object' && mistake.word !== null) {
        wordText = mistake.word.word || mistake.word.words || mistake.word.english || '未知单词';
      } else {
        wordText = mistake.word || mistake.question || mistake.english || '未知单词';
      }

      // 处理用户答案
      let userAnswerText = '';
      if (typeof mistake.selectedAnswer === 'object' && mistake.selectedAnswer !== null) {
        userAnswerText = mistake.selectedAnswer.word || mistake.selectedAnswer.meaning || '未知答案';
      } else {
        userAnswerText = mistake.selectedAnswer || mistake.userAnswer || '未答';
      }

      // 处理正确答案
      let correctAnswerText = '';
      if (typeof mistake.correctAnswer === 'object' && mistake.correctAnswer !== null) {
        correctAnswerText = mistake.correctAnswer.word || mistake.correctAnswer.meaning || '未知答案';
      } else {
        correctAnswerText = mistake.correctAnswer || mistake.meaning || mistake.chinese || '未知答案';
      }

      return {
        ...mistake,
        selected: false,
        wordText: wordText,
        userAnswerText: userAnswerText,
        correctAnswerText: correctAnswerText,
        uniqueId: `mistake_${index}_${wordText}` // 添加唯一标识符
      };
    });

    // 为testResult添加预计算的文本
    const detailWithText = {
      ...testResult,
      testModeText: this.getTestModeText(testResult.testMode),
      timeText: this.formatTime(testResult.timestamp),
      durationText: this.formatDuration(testResult.duration)
    };

    this.setData({
      currentTestDetail: detailWithText,
      allMistakes: mistakes,
      selectedMistakes: [],
      showDetailModal: true
    });
  },

  /**
   * 关闭详情弹窗
   */
  closeDetailModal() {
    this.setData({
      showDetailModal: false,
      currentTestDetail: null,
      allMistakes: [],
      selectedMistakes: []
    });
  },

  /**
   * 切换错词选择状态
   */
  toggleMistakeSelection(e) {
    const index = e.currentTarget.dataset.index;
    const allMistakes = [...this.data.allMistakes];
    const selectedMistakes = [...this.data.selectedMistakes];

    // 切换选中状态
    allMistakes[index].selected = !allMistakes[index].selected;

    if (allMistakes[index].selected) {
      selectedMistakes.push(allMistakes[index]);
    } else {
      // 使用uniqueId进行更可靠的匹配
      const existingIndex = selectedMistakes.findIndex(m =>
        m.uniqueId === allMistakes[index].uniqueId
      );
      if (existingIndex > -1) {
        selectedMistakes.splice(existingIndex, 1);
      }
    }

    this.setData({
      allMistakes,
      selectedMistakes
    });
  },

  /**
   * 全选错词
   */
  selectAllMistakes() {
    const allMistakes = this.data.allMistakes.map(mistake => ({
      ...mistake,
      selected: true
    }));
    
    this.setData({
      allMistakes,
      selectedMistakes: [...allMistakes]
    });
  },

  /**
   * 清空选择
   */
  clearMistakeSelection() {
    const allMistakes = this.data.allMistakes.map(mistake => ({
      ...mistake,
      selected: false
    }));
    
    this.setData({
      allMistakes,
      selectedMistakes: []
    });
  },

  /**
   * 基于选中错词重新测试
   */
  retestWithSelectedMistakes() {
    if (this.data.selectedMistakes.length === 0) {
      wx.showToast({ title: '请先选择错词', icon: 'none' });
      return;
    }

    this.setData({
      showRetestModal: true
    });
  },

  /**
   * 关闭重新测试弹窗
   */
  closeRetestModal() {
    this.setData({
      showRetestModal: false
    });
  },

  /**
   * 选择测试模式
   */
  selectTestMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({ 
      selectedTestMode: mode,
      selectedTestModeText: this.getTestModeText(mode)
    });
  },

  /**
   * 选择练习/测试模式
   */
  selectPracticeMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({ 
      selectedPracticeMode: mode,
      selectedPracticeModeText: mode === 'practice' ? '练习' : '测试'
    });
  },

  /**
   * 选择分享选项
   */
  selectShareOption(e) {
    const option = e.currentTarget.dataset.option;
    this.setData({ selectedShareOption: option });
  },

  /**
   * 确认重新测试
   */
  confirmRetest() {
    const { selectedMistakes, selectedTestMode, selectedPracticeMode, selectedShareOption } = this.data;
    
    if (selectedMistakes.length === 0) {
      wx.showToast({ title: '请先选择错词', icon: 'none' });
      return;
    }

    // 转换错词数据为标准格式
    const words = this.convertMistakesToWords(selectedMistakes);
    
    if (selectedShareOption === 'self') {
      // 自己测试
      this.startSelfTest(words, selectedTestMode, selectedPracticeMode);
    } else {
      // 分享给他人测试
      this.createShareTest(words, selectedTestMode, selectedPracticeMode);
    }
  },

  /**
   * 转换错词数据为标准单词格式
   */
  convertMistakesToWords(mistakes) {
    return mistakes.map(mistake => {
      if (mistake.word && mistake.meaning) {
        // 标准的单词错题
        return {
          word: mistake.word,
          meaning: mistake.meaning,
          phonetic: mistake.phonetic || '',
          example: mistake.example || ''
        };
      } else if (mistake.english && mistake.chinese) {
        // 消消乐错题
        return {
          word: mistake.english,
          meaning: mistake.chinese,
          phonetic: '',
          example: ''
        };
      } else {
        // 其他格式，尝试解析
        return {
          word: mistake.correctAnswer || mistake.word || '未知',
          meaning: mistake.userAnswer || mistake.meaning || '未知',
          phonetic: '',
          example: ''
        };
      }
    });
  },

  /**
   * 开始自己测试
   */
  startSelfTest(words, testMode, practiceMode) {
    // 保存数据到全局
    const app = getApp();
    app.globalData.selectedWords = words;
    
    // 关闭弹窗
    this.closeRetestModal();
    this.closeDetailModal();
    
    // 根据测试模式跳转到对应页面
    let url = '';
    
    if (testMode === 'elimination') {
      // 消消乐模式
      url = `/pages/wordtest/mode-select/mode-select?retestMode=elimination&practiceMode=${practiceMode}`;
    } else if (testMode === 'dictation') {
      // 听写模式
      url = `/pages/spelling/practice/practice?mode=${practiceMode}&retestWords=true`;
    } else {
      // 英译汉/汉译英模式
      const mode = practiceMode === 'practice' ? 'practice' : 'test';
      url = `/pages/wordtest/test/test?testMode=${testMode}&shareMode=self&timeLimit=unlimited&retestWords=true&mode=${mode}`;
    }
    
    wx.navigateTo({
      url: url,
      success: () => {
        wx.showToast({ title: '正在准备测试...', icon: 'loading' });
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  /**
   * 创建分享测试
   */
  async createShareTest(words, testMode, practiceMode) {
    wx.showLoading({ title: '创建分享测试...' });

    try {
      // 生成分享ID
      const shareId = 'retest_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      const currentUser = wx.getStorageSync('userInfo') || {};

      // 创建分享测试数据
      const shareTestData = {
        shareId: shareId,
        testMode: testMode,
        practiceMode: practiceMode,
        libraryId: 'mistake_review',
        libraryName: '错词重测',
        words: words,
        createdBy: currentUser.nickName || '匿名用户',
        creatorInfo: {
          openid: currentUser.openid,
          nickName: currentUser.nickName,
          avatarUrl: currentUser.avatarUrl
        },
        createTime: Date.now(),
        visitors: [],
        results: []
      };

      // 保存到本地存储（只保存必要的元数据）
      try {
        const shareTests = wx.getStorageSync('shareTests') || {};

        // 为了避免本地存储大小限制，只保存必要的元数据
        const lightShareTestData = {
          shareId: shareTestData.shareId,
          testMode: shareTestData.testMode,
          libraryId: shareTestData.libraryId,
          libraryName: shareTestData.libraryName,
          wordsCount: shareTestData.words ? shareTestData.words.length : 0, // 只保存词汇数量
          practiceMode: practiceMode,
          createdBy: shareTestData.createdBy,
          creatorInfo: shareTestData.creatorInfo,
          createTime: shareTestData.createTime,
          // 不保存完整的words数组，以节省存储空间
          visitors: [],
          results: []
        };

        shareTests[shareId] = lightShareTestData;
        wx.setStorageSync('shareTests', shareTests);
        console.log('错词重测分享测试已保存到本地:', shareId, '词汇数量:', lightShareTestData.wordsCount);
      } catch (error) {
        console.error('保存到本地存储失败:', error);
        console.warn('本地存储失败，但云端保存成功，分享功能仍可正常使用');
      }

      // 保存到云端（必须成功）
      try {
        // 错词重测不能使用索引存储，因为错词数据不依赖词库序号
        // 错词本中存储的是完整的词汇信息，必须传递完整数据
        const useIndexBasedStorage = false;

        await wx.cloud.callFunction({
          name: 'createShareTest',
          data: {
            shareId: shareTestData.shareId,
            testType: shareTestData.testMode,
            // 超级优化：基于索引存储时只传递必要信息
            words: useIndexBasedStorage ?
              [{
                _id: shareTestData.words[0]._id,
                words: shareTestData.words[0].words,
                totalCount: shareTestData.words.length
              }] :
              shareTestData.words,
            libraryId: shareTestData.libraryId,
            libraryName: shareTestData.libraryName,
            // 错词重测通常不需要乱序，但为了兼容性还是传递
            isRandomOrder: false,
            settings: { practiceMode: practiceMode },
            expireDays: 7,
            wordsPerGroup: 20
          }
        });
        console.log('错词重测分享测试已成功保存到云端和本地');
      } catch (err) {
        console.error('云端保存失败:', err);
        wx.hideLoading();
        wx.showModal({
          title: '保存失败',
          content: '分享测试保存到云端失败，可能是网络问题。请检查网络连接后重试。',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }

      wx.hideLoading();
      
      // 关闭弹窗
      this.closeRetestModal();
      this.closeDetailModal();
      
      // 显示分享选项
      const testModeText = this.getTestModeText(testMode);
      this.showShareOptions({
        title: `${testModeText}分享测试已创建`,
        content: `分享ID: ${shareId}\n模式: ${testModeText}\n单词数: ${words.length}个`,
        shareId: shareId,
        shareData: {
          testMode: testMode,
          testName: `${testModeText}错词重测 - ${words.length}个单词`,
          shareId: shareId
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('创建分享测试失败:', error);
      wx.showToast({ title: '创建失败', icon: 'error' });
    }
  },

  /**
   * 删除测试记录
   */
  deleteTestRecord(e) {
    const index = e.currentTarget.dataset.index;
    const testResult = this.data.testResults[index];
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除这条测试记录吗？\n\n${this.getTestModeText(testResult.testMode)} - ${this.formatTime(testResult.timestamp)}`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteTestRecord(index);
        }
      }
    });
  },

  /**
   * 执行删除测试记录
   */
  doDeleteTestRecord(index) {
    wx.showLoading({ title: '删除中...' });
    
    try {
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      const currentUserOpenid = currentUser.openid;
      
      const testResult = this.data.testResults[index];
      
      // 获取所有测试结果
      const allTestResults = wx.getStorageSync('testResults') || [];
      
      // 标记为删除
      const updatedResults = allTestResults.map(result => {
        if (result.shareMode === 'share' && 
            result.creatorOpenid === currentUserOpenid && 
            result.participantOpenid === this.data.userOpenid &&
            result.timestamp === testResult.timestamp) {
          return { ...result, isDeleted: true };
        }
        return result;
      });
      
      // 保存更新
      wx.setStorageSync('testResults', updatedResults);
      
      // 更新页面数据
      const newTestResults = [...this.data.testResults];
      newTestResults.splice(index, 1);
      
      // 重新计算统计数据
      const newStatistics = this.calculateStatistics(newTestResults);
      
      this.setData({
        testResults: newTestResults,
        statistics: newStatistics
      });
      
      wx.hideLoading();
      wx.showToast({ title: '删除成功', icon: 'success' });
      
    } catch (error) {
      wx.hideLoading();
      console.error('删除失败:', error);
      wx.showToast({ title: '删除失败', icon: 'error' });
    }
  },

  /**
   * 显示分享成功弹窗
   */
  showShareSuccessModal() {
    wx.showModal({
      title: '分享成功',
      content: '测试已成功分享给好友！',
      cancelText: '返回',
      confirmText: '查看分享页',
      success: (res) => {
        if (res.confirm) {
          // 用户点击"查看分享页"
          wx.navigateTo({
            url: '/pages/profile/share/share',
            success: () => {
              console.log('跳转到我的分享页面成功');
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              wx.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        } else if (res.cancel) {
          // 用户点击"返回"，不做任何操作，保持在当前页面
          console.log('用户选择返回当前页面');
        }
      }
    });
  }
});