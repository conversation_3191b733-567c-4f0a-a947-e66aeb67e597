Page({
  data: {
    students: [],
    loading: true,
    showAddDialog: false,
    showScoreDialog: false,
    selectedStudent: null,

    // 添加学生表单
    newStudent: {
      name: '',
      gender: '',
      genderIndex: -1,
      school: ''
    },

    // 性别选项
    genderOptions: ['男', '女'],



    // 管理模式
    isManageMode: false,
    selectedStudents: [],
    
    // 添加成绩表单
    newScore: {
      examName: '',
      score: '',
      totalScore: 100,
      examDate: ''
    },
    
    // 筛选
    filterGrade: '',
    filterClass: '',
    
    // 统计数据
    stats: {
      totalStudents: 0,
      maxScore: 0, // 添加maxScore初始值
      improvedCount: 0,
      declinedCount: 0
    }
  },

  onLoad() {
    console.log('=== 学生管理页面加载 ===');
    try {
      wx.setNavigationBarTitle({
        title: '学生管理'
      });
      this.loadStudents();
    } catch (error) {
      console.error('页面加载失败:', error);
    }
  },

  // 成绩换算工具函数
  normalizeScore(score, totalScore, targetTotal = 150) {
    if (totalScore === targetTotal) {
      return score;
    }
    return Math.round((score / totalScore) * targetTotal * 10) / 10;
  },

  // 格式化成绩显示
  formatScoreDisplay(score, totalScore) {
    if (totalScore === 150) {
      return `${score}/150`;
    } else {
      const normalizedScore = this.normalizeScore(score, totalScore, 150);
      const normalizedTotal = 150;
      return `${normalizedScore}/${normalizedTotal}(换算)`;
    }
  },

  onShow() {
    console.log('=== 学生管理页面显示 ===');
    try {
      this.loadStudents();
    } catch (error) {
      console.error('页面显示失败:', error);
    }
  },

  onHide() {
    console.log('=== 学生管理页面隐藏 ===');
  },

  onUnload() {
    console.log('=== 学生管理页面卸载 ===');
  },

  onError(error) {
    console.error('=== 页面错误 ===:', error);
  },

  // 加载学生数据
  async loadStudents() {
    // 设置加载状态，同时确保统计数据有初始值
    this.setData({
      loading: true,
      stats: {
        totalStudents: 0,
        maxScore: 0,
        improvedCount: 0,
        declinedCount: 0
      }
    });

    wx.showLoading({ title: '加载中...' });

    try {
      const result = await wx.cloud.callFunction({
        name: 'getStudentList',
        data: {}
      });

      if (result.result && result.result.success) {
        const students = result.result.data || [];
        
        // 计算每个学生的统计数据
        const processedStudents = students.map(student => {
          const scores = student.scores || [];

          // 使用换算后的成绩计算平均分
          const normalizedScores = scores.map(score =>
            this.normalizeScore(score.score, score.totalScore, 150)
          );
          const totalAvg = normalizedScores.length > 0 ?
            normalizedScores.reduce((sum, score) => sum + score, 0) / normalizedScores.length : 0;

          // 计算趋势（使用换算后的成绩比较）
          let trend = 'stable';
          if (scores.length >= 2) {
            // scores现在是按日期倒序排列，scores[0]是最新的，scores[1]是上一次的
            const latestNormalized = this.normalizeScore(scores[0].score, scores[0].totalScore, 150);
            const previousNormalized = this.normalizeScore(scores[1].score, scores[1].totalScore, 150);
            const improvement = latestNormalized - previousNormalized;
            console.log(`学生${student.name}: 最新${latestNormalized}(换算), 上次${previousNormalized}(换算), 差值${improvement}`);
            if (improvement > 0) trend = 'up';
            else if (improvement < 0) trend = 'down';
            console.log(`学生${student.name}的趋势: ${trend}`);
          }

          // 格式化成绩显示
          const recentScoreDisplay = scores.length > 0 ?
            this.formatScoreDisplay(scores[0].score, scores[0].totalScore) : '0/150';
          const previousScoreDisplay = scores.length >= 2 ?
            this.formatScoreDisplay(scores[1].score, scores[1].totalScore) : '0/150';

          return {
            ...student,
            totalAvg: Math.round(totalAvg * 10) / 10,
            recentScore: scores.length > 0 ? scores[0].score : 0,  // 原始分数（用于内部计算）
            previousScore: scores.length >= 2 ? scores[1].score : 0,  // 原始分数（用于内部计算）
            recentScoreDisplay: recentScoreDisplay,  // 格式化显示
            previousScoreDisplay: previousScoreDisplay,  // 格式化显示
            trend: trend,
            scoreCount: scores.length,
            selected: false // 添加选中状态
          };
        });

        // 计算统计数据
        const stats = this.calculateStats(processedStudents, students);
        
        this.setData({
          students: processedStudents,
          stats: stats,
          loading: false
        });
      } else {
        this.setData({
          students: [],
          loading: false
        });
      }
    } catch (error) {
      console.error('加载学生数据失败:', error);
      this.setData({
        students: [],
        loading: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 计算统计数据
  calculateStats(processedStudents, originalStudents) {
    if (processedStudents.length === 0) {
      return {
        totalStudents: 0,
        maxScore: 0,
        improvedCount: 0,
        declinedCount: 0
      };
    }

    const totalStudents = processedStudents.length;

    // 计算所有学生所有考试中的最高分（使用换算后的成绩）
    let maxScore = 0;
    originalStudents.forEach(student => {
      if (student.scores && student.scores.length > 0) {
        const normalizedScores = student.scores.map(score =>
          this.normalizeScore(score.score, score.totalScore, 150)
        );
        const studentMaxScore = Math.max(...normalizedScores);
        maxScore = Math.max(maxScore, studentMaxScore);
      }
    });
    const improvedStudents = processedStudents.filter(student => student.trend === 'up');
    const declinedStudents = processedStudents.filter(student => student.trend === 'down');
    const improvedCount = improvedStudents.length;
    const declinedCount = declinedStudents.length;

    console.log('进步学生:', improvedStudents.map(s => s.name));
    console.log('退步学生:', declinedStudents.map(s => s.name));
    console.log(`进步人数: ${improvedCount}, 退步人数: ${declinedCount}`);

    return {
      totalStudents,
      maxScore: maxScore,
      improvedCount,
      declinedCount
    };
  },

  // 显示添加学生对话框
  showAddStudentDialog() {
    console.log('=== 点击添加学生按钮 ===');
    try {
      console.log('准备显示添加学生对话框');
      this.setData({
        showAddDialog: true,
        newStudent: {
          name: '',
          gender: '',
          genderIndex: -1,
          school: ''
        }
      });
      console.log('添加学生对话框数据设置完成');
    } catch (error) {
      console.error('显示添加学生对话框失败:', error);
    }
  },

  // 隐藏添加学生对话框
  hideAddStudentDialog() {
    console.log('=== 隐藏添加学生对话框 ===');
    try {
      this.setData({
        showAddDialog: false,
        newStudent: {
          name: '',
          gender: '',
          genderIndex: -1,
          school: ''
        }
      });
      console.log('添加学生对话框已隐藏');
    } catch (error) {
      console.error('隐藏添加学生对话框失败:', error);
    }
  },



  // 输入学生信息
  onStudentInput(e) {
    console.log('=== 输入学生信息 ===');
    try {
      const { field } = e.currentTarget.dataset;
      const value = e.detail.value;
      console.log('输入字段:', field, '值:', value);

      this.setData({
        [`newStudent.${field}`]: value
      });
      console.log('输入数据更新成功');
    } catch (error) {
      console.error('输入处理失败:', error);
    }
  },

  // 选择器变化
  onStudentPickerChange(e) {
    console.log('=== 选择器变化 ===');
    try {
      const { field } = e.currentTarget.dataset;
      const index = parseInt(e.detail.value);
      console.log('选择器字段:', field, '索引:', index);

      if (field === 'gender') {
        this.setData({
          'newStudent.gender': this.data.genderOptions[index],
          'newStudent.genderIndex': index
        });
        console.log('性别选择成功:', this.data.genderOptions[index]);
      }
    } catch (error) {
      console.error('选择器处理失败:', error);
    }
  },

  // 输入框获得焦点
  onInputFocus(e) {
    console.log('=== 输入框获得焦点 ===');
    const field = e.currentTarget.dataset.field;
    console.log('焦点字段:', field);

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
  },

  // 输入框失去焦点
  onInputBlur(e) {
    console.log('=== 输入框失去焦点 ===');
    const field = e.currentTarget.dataset.field;
    console.log('失焦字段:', field);

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation();
  },

  // 输入框错误
  onInputError(e) {
    console.error('=== 输入框错误 ===:', e);
  },

  // 模态框遮罩层点击
  onModalOverlayClick(e) {
    console.log('=== 模态框遮罩层点击 ===');
    this.hideAddStudentDialog();
  },

  // 模态框内容区点击
  onModalContentClick(e) {
    console.log('=== 模态框内容区点击 ===');
    // 阻止事件冒泡，不关闭对话框
  },

  // 成绩模态框遮罩层点击
  onScoreModalOverlayClick(e) {
    console.log('=== 成绩模态框遮罩层点击 ===');
    this.hideAddScoreDialog();
  },

  // 成绩模态框内容区点击
  onScoreModalContentClick(e) {
    console.log('=== 成绩模态框内容区点击 ===');
    // 阻止事件冒泡，不关闭对话框
  },







  // 添加学生
  async addStudent() {
    const { newStudent } = this.data;

    console.log('添加学生数据:', newStudent);

    // 验证输入
    if (!newStudent.name || !newStudent.name.trim()) {
      console.log('姓名验证失败:', newStudent.name);
      wx.showToast({
        title: '请输入学生姓名',
        icon: 'none'
      });
      return;
    }

    if (!newStudent.gender) {
      console.log('性别验证失败:', newStudent.gender);
      wx.showToast({
        title: '请选择性别',
        icon: 'none'
      });
      return;
    }

    if (!newStudent.school || !newStudent.school.trim()) {
      console.log('学校验证失败:', newStudent.school);
      wx.showToast({
        title: '请输入学校名称',
        icon: 'none'
      });
      return;
    }

    console.log('验证通过，准备调用云函数');

    wx.showLoading({ title: '添加中...' });

    try {
      const studentData = {
        name: newStudent.name.trim(),
        gender: newStudent.gender,
        school: newStudent.school.trim()
      };

      console.log('准备调用云函数，参数:', studentData);

      const result = await wx.cloud.callFunction({
        name: 'addStudent',
        data: studentData
      });

      console.log('云函数调用结果:', result);
      console.log('云函数返回详情:', JSON.stringify(result, null, 2));
      wx.hideLoading();

      if (result.result && result.result.success) {
        console.log('添加学生成功');
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
        this.hideAddStudentDialog();
        this.loadStudents();
      } else {
        console.log('添加学生失败，错误信息:', result.result?.message);
        console.log('完整的失败结果:', result.result);
        wx.showToast({
          title: result.result?.message || '添加失败',
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('添加学生异常:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        errCode: error.errCode,
        errMsg: error.errMsg
      });

      wx.showToast({
        title: `添加失败: ${error.message || error.errMsg || '未知错误'}`,
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 查看学生详情
  viewStudentDetail(e) {
    const studentId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `./student-detail/student-detail?studentId=${studentId}`
    });
  },

  // 显示添加成绩对话框
  showAddScoreDialog(e) {
    const student = e.currentTarget.dataset.student;
    const today = new Date();
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    
    this.setData({
      showScoreDialog: true,
      selectedStudent: student,
      newScore: {
        examName: '',
        score: '',
        totalScore: 150,
        grade: '',
        semester: '',
        examDate: dateStr
      }
    });
  },

  // 隐藏添加成绩对话框
  hideAddScoreDialog() {
    this.setData({
      showScoreDialog: false,
      selectedStudent: null
    });
  },

  // 输入成绩信息
  onScoreInput(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [`newScore.${field}`]: value
    });
  },

  // 添加成绩
  async addScore() {
    const { selectedStudent, newScore } = this.data;
    
    // 验证输入
    if (!newScore.examName.trim()) {
      wx.showToast({
        title: '请输入考试名称',
        icon: 'none'
      });
      return;
    }

    if (!newScore.grade.trim()) {
      wx.showToast({
        title: '请输入年级',
        icon: 'none'
      });
      return;
    }

    if (!newScore.semester.trim()) {
      wx.showToast({
        title: '请输入学期',
        icon: 'none'
      });
      return;
    }

    if (!newScore.examDate) {
      wx.showToast({
        title: '请选择考试日期',
        icon: 'none'
      });
      return;
    }

    const score = parseFloat(newScore.score);
    const totalScore = parseFloat(newScore.totalScore);
    
    if (isNaN(score) || score < 0) {
      wx.showToast({
        title: '请输入有效分数',
        icon: 'none'
      });
      return;
    }

    if (isNaN(totalScore) || totalScore <= 0) {
      wx.showToast({
        title: '请输入有效总分',
        icon: 'none'
      });
      return;
    }

    if (score > totalScore) {
      wx.showToast({
        title: '分数不能超过总分',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '添加中...' });

    try {
      const result = await wx.cloud.callFunction({
        name: 'addStudentScore',
        data: {
          studentId: selectedStudent._id,
          examName: newScore.examName.trim(),
          score: score,
          totalScore: totalScore,
          grade: newScore.grade.trim(),
          semester: newScore.semester.trim(),
          examDate: newScore.examDate
        }
      });

      if (result.result && result.result.success) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
        this.hideAddScoreDialog();
        this.loadStudents();
      } else {
        wx.showToast({
          title: result.result?.message || '添加失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('添加成绩失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },



  // 切换管理模式
  toggleManageMode() {
    const newManageMode = !this.data.isManageMode;

    // 如果退出管理模式，清除所有选中状态
    if (!newManageMode) {
      const students = this.data.students.map(student => ({
        ...student,
        selected: false
      }));

      this.setData({
        students: students,
        selectedStudents: []
      });
    }

    this.setData({
      isManageMode: newManageMode
    });
  },

  // 切换学生选中状态
  toggleStudentSelection(e) {
    if (!this.data.isManageMode) return;

    const studentId = e.currentTarget.dataset.id;
    const students = this.data.students.map(student => {
      if (student._id === studentId) {
        return {
          ...student,
          selected: !student.selected
        };
      }
      return student;
    });

    // 更新选中的学生列表
    const selectedStudents = students.filter(student => student.selected).map(student => student._id);

    this.setData({
      students: students,
      selectedStudents: selectedStudents
    });
  },

  // 批量删除学生
  async batchDeleteStudents() {
    const { selectedStudents } = this.data;

    if (selectedStudents.length === 0) {
      wx.showToast({
        title: '请先选择要删除的学生',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedStudents.length} 个学生吗？删除后相关的成绩记录也会一并删除。`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });

          try {
            console.log('=== 开始批量删除学生 ===');
            console.log('选中的学生IDs:', selectedStudents);

            const result = await wx.cloud.callFunction({
              name: 'batchDeleteStudents',
              data: {
                studentIds: selectedStudents
              }
            });

            console.log('云函数返回结果:', result);

            if (result.result && result.result.success) {
              console.log('删除成功');
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              // 退出管理模式并重新加载数据
              this.setData({
                isManageMode: false,
                selectedStudents: []
              });

              this.loadStudents();
            } else {
              console.log('删除失败:', result.result);
              wx.showToast({
                title: result.result?.message || '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('批量删除学生失败:', error);
            wx.showToast({
              title: '删除失败: ' + error.message,
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  }
});
