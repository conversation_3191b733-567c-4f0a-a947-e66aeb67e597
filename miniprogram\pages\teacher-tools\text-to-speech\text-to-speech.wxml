<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">文本转语音</view>
    <view class="subtitle">专业语音合成工具</view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="section-title">输入文本</view>
    <textarea 
      class="text-input"
      placeholder="请输入要转换为语音的文本内容..."
      value="{{inputText}}"
      bindinput="onTextInput"
      maxlength="{{maxLength}}"
      auto-height
    />
    <view class="text-counter">{{inputText.length}}/{{maxLength}}</view>
  </view>

  <!-- 语音设置 -->
  <view class="settings-section">
    <view class="section-title">语音设置</view>
    
    <!-- 语音选择 -->
    <view class="setting-item">
      <view class="setting-label">语音选择</view>
      <picker 
        class="setting-picker"
        bindchange="onVoiceChange"
        value="{{voiceIndex}}"
        range="{{voiceOptions}}"
        range-key="description"
      >
        <view class="picker-display">
          {{voiceOptions[voiceIndex].description}}
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <!-- 语速设置 -->
    <view class="setting-item">
      <view class="setting-label">语速: {{speechRate}}x</view>
      <slider 
        class="setting-slider"
        value="{{speechRate}}"
        min="0.5"
        max="2.0"
        step="0.1"
        bindchange="onRateChange"
        activeColor="#007aff"
        backgroundColor="#e9e9e9"
        block-size="20"
      />
    </view>

    <!-- 音调设置 -->
    <view class="setting-item">
      <view class="setting-label">音调: {{pitch}}%</view>
      <slider 
        class="setting-slider"
        value="{{pitch}}"
        min="50"
        max="150"
        step="5"
        bindchange="onPitchChange"
        activeColor="#007aff"
        backgroundColor="#e9e9e9"
        block-size="20"
      />
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons-row">
      <button
        class="action-btn primary {{generating ? 'loading' : ''}}"
        bindtap="onGenerate"
        disabled="{{!canGenerate}}"
      >
        <text wx:if="{{!generating}}">🎵生成</text>
        <text wx:else>⏳生成中</text>
      </button>

      <button
        class="action-btn secondary"
        bindtap="onPreview"
        disabled="{{!canPreview}}"
      >
        <text>{{isPlaying ? '⏸️暂停' : '▶️预览'}}</text>
      </button>

      <button
        class="action-btn success"
        bindtap="onDownload"
        disabled="{{!canDownload}}"
      >
        <text>🔗复制</text>
      </button>
    </view>
  </view>



  <!-- 使用说明 -->
  <view class="tips-section">
    <view class="section-title">使用说明</view>
    <view class="tips-list">
      <view class="tip-item">
        <text class="tip-icon">💡</text>
        <text class="tip-text">支持中英文混合文本，最多{{maxLength}}字符</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🎭</text>
        <text class="tip-text">提供多种语音选择，包括美式、英式发音</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">⚙️</text>
        <text class="tip-text">可调节语速(0.5-2.0倍)和音调(50-150%)</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🔗</text>
        <text class="tip-text">点击"复制"获取下载链接，在浏览器中打开即可下载</text>
      </view>
    </view>
  </view>
</view>
