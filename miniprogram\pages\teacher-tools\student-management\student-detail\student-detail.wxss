/* 学生详情页面样式 */
.container {
  padding: 20rpx;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 学生信息卡片 */
.student-info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.student-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.student-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4F46E5, #7C3AED);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
}

.student-basic {
  flex: 1;
}

.student-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.student-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  font-size: 26rpx;
  color: #666;
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #4F46E5;
  margin-bottom: 8rpx;
}

.stat-number.positive {
  color: #10B981;
}

.stat-number.negative {
  color: #EF4444;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 图表卡片 */
.chart-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.trend-icon.up {
  color: #10B981;
}

.trend-icon.down {
  color: #EF4444;
}

.trend-icon {
  color: #666;
  font-size: 24rpx;
}

.trend-text {
  font-size: 24rpx;
  color: #666;
}

.chart-container {
  width: 100%;
  height: 400rpx;
  position: relative;
  z-index: 1; /* 确保图表在较低层级 */
}

.score-chart {
  width: 100%;
  height: 100%;
}

/* 成绩卡片 */
.scores-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.add-score-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
  background: #4F46E5;
  color: white;
  border: none;
}

/* 成绩列表 */
.scores-list {
  margin-top: 30rpx;
}

.score-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}

.score-item:last-child {
  border-bottom: none;
}

.score-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.score-info {
  flex: 1;
  min-width: 0;
}

.exam-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.exam-details {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.score-display {
  text-align: right;
  flex-shrink: 0;
  margin-left: 20rpx;
}

.score-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #4F46E5;
}

.score-percent {
  font-size: 22rpx;
  color: #666;
  margin-left: 8rpx;
}

.score-actions {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 10rpx 20rpx;
  font-size: 22rpx;
  border-radius: 12rpx;
  border: none;
  white-space: nowrap;
}

.action-btn.edit {
  background: #f3f4f6;
  color: #666;
}

.action-btn.delete {
  background: #fee2e2;
  color: #dc2626;
}

/* 空状态 */
.empty-scores {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #666;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* 提高z-index确保在最顶层 */
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  box-sizing: border-box;
  color: #333;
  line-height: 1.5;
  min-height: 80rpx;
  display: block;
}

.form-input.picker {
  display: flex;
  align-items: center;
  color: #333;
}

.form-input:focus {
  border-color: #4F46E5;
  background: white;
}

.input-placeholder {
  color: #999 !important;
  font-size: 28rpx !important;
  opacity: 1;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 2rpx solid #f3f4f6;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f3f4f6;
  color: #666;
}

.modal-btn.confirm {
  background: #4F46E5;
  color: white;
}
