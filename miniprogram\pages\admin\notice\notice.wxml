<view class="container">
  <!-- 当前通知显示 -->
  <view class="current-notice-section">
    <view class="section-title">
      <text>当前首页通知</text>
      <text class="subtitle">（用户在首页看到的通知内容）</text>
    </view>
    
    <view wx:if="{{currentNoticeLoading}}" class="current-notice-loading">
      <view class="loading-text">加载中...</view>
    </view>
    
    <view wx:elif="{{!currentNotice}}" class="current-notice-empty">
      <view class="empty-icon">📭</view>
      <view class="empty-text">当前没有生效的通知</view>
      <view class="empty-hint">请在下方添加新通知或启用已有通知</view>
    </view>
    
    <view wx:else class="current-notice-content">
      <view class="current-notice-header">
        <view class="current-notice-badge">
          <text class="badge-text">正在显示</text>
        </view>
        <button class="edit-current-btn" bindtap="editCurrentNotice">快速编辑</button>
      </view>
      <view class="current-notice-text">{{currentNotice.content}}</view>
      <view class="current-notice-time">
        更新时间：<wxs module="utils">
          module.exports = {
            formatTime: function(time) {
              if (!time) return '';
              
              var date = getDate(time);
              var now = getDate();
              var diffTime = now - date;
              var diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
              
              if (diffDays === 0) {
                var hours = date.getHours().toString();
                var minutes = date.getMinutes().toString();
                if (hours.length === 1) hours = '0' + hours;
                if (minutes.length === 1) minutes = '0' + minutes;
                return '今天 ' + hours + ':' + minutes;
              } else if (diffDays === 1) {
                var hours = date.getHours().toString();
                var minutes = date.getMinutes().toString();
                if (hours.length === 1) hours = '0' + hours;
                if (minutes.length === 1) minutes = '0' + minutes;
                return '昨天 ' + hours + ':' + minutes;
              } else if (diffDays < 7) {
                return diffDays + '天前';
              } else {
                return (date.getMonth() + 1) + '月' + date.getDate() + '日';
              }
            }
          }
        </wxs>{{utils.formatTime(currentNotice.updateTime || currentNotice.createTime)}}
      </view>
    </view>
  </view>

  <!-- 添加新通知 -->
  <view class="add-notice-section">
    <view class="section-title">添加新通知</view>
    <textarea 
      class="notice-input" 
      placeholder="请输入通知内容..."
      value="{{newNoticeContent}}"
      bindinput="onNewNoticeInput"
      maxlength="200"
      show-confirm-bar="{{false}}"
    ></textarea>
    <button class="add-btn" bindtap="addNotice" disabled="{{!newNoticeContent.trim()}}">
      发布通知
    </button>
  </view>

  <!-- 通知列表 -->
  <view class="notices-section">
    <view class="section-title">通知管理</view>
    
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>
    
    <view wx:elif="{{notices.length === 0}}" class="empty-container">
      <view class="empty-text">暂无通知</view>
    </view>
    
    <view wx:else class="notices-list">
      <view 
        wx:for="{{notices}}" 
        wx:key="_id" 
        class="notice-item {{item.status === 'active' ? 'active' : 'inactive'}}"
        bindtap="onNoticeeTap"
        data-index="{{index}}"
      >
        <view class="notice-header">
          <view class="notice-status">
            <text class="status-badge {{item.status}}">
              {{item.status === 'active' ? '启用中' : '已停用'}}
            </text>
          </view>
          <view class="notice-time">
            {{utils.formatTime(item.updateTime || item.createTime)}}
          </view>
        </view>
        <view class="notice-content">{{item.content}}</view>
      </view>
    </view>
  </view>
</view> 