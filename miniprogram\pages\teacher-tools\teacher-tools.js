Page({
  data: {
    tools: [
      {
        id: 'exam_analysis',
        title: '试卷分析报告生成',
        subtitle: '智能分析薄弱环节',
        description: '全面分析学生答题情况，识别知识薄弱点',
        icon: '📊',
        color: 'blue',
        route: '/pages/profile/report/report'
      },
      {
        id: 'student_management',
        title: '学生管理',
        subtitle: '班级学情管理',
        description: '记录学生基本信息和考试成绩，统计分析学习进步情况',
        icon: '👨‍🎓',
        color: 'green',
        route: './student-management/student-management'
      },
      {
        id: 'text_to_speech',
        title: '文本转语音',
        subtitle: '专业语音合成工具',
        description: '支持多种语音、可调节语速音调，生成高质量音频文件',
        icon: '🎤',
        color: 'purple',
        route: './text-to-speech/text-to-speech'
      },
      {
        id: 'article_analysis',
        title: '文章词汇识别标记',
        subtitle: '智能词汇分析标注',
        description: '识别文章中的高考/四六级词汇并进行标注',
        icon: '📝',
        color: 'teal',
        route: './article-analysis/article-analysis'
      },
      {
        id: 'audio_processor',
        title: '听口音频处理',
        subtitle: '智能音频分割工具',
        description: '自动分割听口训练音频，批量处理题目',
        icon: '🎵',
        color: 'orange',
        route: './audio-processor/audio-processor',
        adminOnly: true // 仅管理员可见
      }
    ],
    isLoggedIn: false,
    isAdmin: false
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '教师工具箱'
    });

    // 调试：打印工具数据
    console.log('教师工具箱工具列表:', this.data.tools);

    // 检查登录状态和管理员权限
    this.checkLoginStatus();
    this.filterToolsByPermission();
  },

  onShow() {
    // 每次显示时检查登录状态
    console.log('教师工具箱页面显示，检查登录状态');
    this.checkLoginStatus();
    this.filterToolsByPermission();
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp();
    
    // 重新检查登录状态，确保最新数据
    app.checkLoginStatus();
    
    setTimeout(() => {
      const isLoggedIn = app.isLoggedIn();
      const userInfo = app.globalData.userInfo;
      
      console.log('教师工具箱检查登录状态:', isLoggedIn, userInfo);
      
      if (!isLoggedIn || !userInfo) {
        this.setData({
          isLoggedIn: false
        });
        
        wx.showModal({
          title: '需要登录',
          content: '使用教师工具箱需要先登录账号',
          confirmText: '去登录',
          cancelText: '返回',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/login/login'
              });
            } else {
              wx.navigateBack();
            }
          }
        });
        return;
      }
      
      this.setData({
        isLoggedIn: true
      });

      // 检查管理员权限
      this.checkAdminPermission(userInfo);
    }, 100);
  },

  // 检查管理员权限
  checkAdminPermission(userInfo) {
    // 管理员判断逻辑
    const adminUsers = [
      'oKPaP7dzbpGC16RdvLyuLhZUM38E', // 你的微信openid
      'admin_openid_2' // 可以添加其他管理员openid
    ];

    const isAdmin = adminUsers.includes(userInfo?.openid) ||
                   userInfo?.nickName === '管理员' ||
                   userInfo?.isAdmin === true ||
                   userInfo?.username === 'admin'; // 添加用户名判断

    console.log('管理员权限检查:', {
      openid: userInfo?.openid,
      nickName: userInfo?.nickName,
      username: userInfo?.username,
      isAdmin: isAdmin
    });

    this.setData({
      isAdmin: isAdmin
    });
  },

  // 根据权限过滤工具
  filterToolsByPermission() {
    const { isAdmin } = this.data;
    const allTools = [
      {
        id: 'exam_analysis',
        title: '试卷分析报告生成',
        subtitle: '智能分析薄弱环节',
        description: '全面分析学生答题情况，识别知识薄弱点',
        icon: '📊',
        color: 'blue',
        route: '/pages/profile/report/report'
      },
      {
        id: 'student_management',
        title: '学生管理',
        subtitle: '班级学情管理',
        description: '记录学生基本信息和考试成绩，统计分析学习进步情况',
        icon: '👨‍🎓',
        color: 'green',
        route: './student-management/student-management'
      },
      {
        id: 'text_to_speech',
        title: '文本转语音',
        subtitle: '专业语音合成工具',
        description: '支持多种语音、可调节语速音调，生成高质量音频文件',
        icon: '🎤',
        color: 'purple',
        route: './text-to-speech/text-to-speech'
      },
      {
        id: 'article_analysis',
        title: '文章词汇识别标记',
        subtitle: '智能词汇分析标注',
        description: '识别文章中的高考/四六级词汇并进行标注',
        icon: '📝',
        color: 'teal',
        route: './article-analysis/article-analysis'
      },
      {
        id: 'audio_processor',
        title: '听口音频处理',
        subtitle: '智能音频分割工具',
        description: '自动分割听口训练音频，批量处理题目',
        icon: '🎵',
        color: 'orange',
        route: './audio-processor/audio-processor',
        adminOnly: true
      }
    ];

    // 过滤工具：非管理员用户隐藏仅管理员可见的工具
    const filteredTools = allTools.filter(tool => {
      if (tool.adminOnly && !isAdmin) {
        return false;
      }
      return true;
    });

    this.setData({
      tools: filteredTools
    });

    console.log('工具过滤完成，可见工具数量:', filteredTools.length);
  },

  // 选择工具
  onToolSelect(e) {
    try {
      const toolId = e.currentTarget.dataset.id;
      console.log('点击工具ID:', toolId);
      console.log('当前工具数据:', this.data.tools);
      
      // 验证工具ID是否存在
      if (!toolId) {
        console.error('工具ID为空');
        wx.showToast({
          title: '功能识别失败',
          icon: 'none'
        });
        return;
      }
      
      const selectedTool = this.data.tools.find(tool => tool.id === toolId);
      console.log('找到的工具:', selectedTool);
      
      // 检查是否找到对应的工具
      if (!selectedTool) {
        console.error('未找到对应的工具:', toolId);
        console.error('可用工具列表:', this.data.tools.map(tool => tool.id));
        wx.showToast({
          title: '功能暂不可用',
          icon: 'none'
        });
        return;
      }
      
      // 检查登录状态
      if (!this.data.isLoggedIn) {
        console.log('用户未登录，重新检查登录状态');
        
        // 重新检查登录状态
        this.checkLoginStatus();
        
        // 如果重新检查后仍然没有登录，则显示提示
        if (!this.data.isLoggedIn) {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }
      }
      
      // 添加触觉反馈
      wx.vibrateShort({
        type: 'light'
      });

      // 检查是否为开发中功能
      if (selectedTool.developing) {
        wx.showModal({
          title: selectedTool.title,
          content: '开发完善中，敬请期待',
          showCancel: false,
          confirmText: '知道了',
          confirmColor: '#007aff'
        });
        return;
      }

      // 跳转到对应页面
      if (selectedTool.route) {
        // 为文章分析功能添加特殊处理
        if (toolId === 'article_analysis') {
          console.log('准备跳转到文章分析页面:', selectedTool.route);
          
          // 尝试多种跳转方式
          wx.navigateTo({
            url: '/pages/teacher-tools/article-analysis/article-analysis',
            success: () => {
              console.log('文章分析页面跳转成功');
            },
            fail: (err) => {
              console.error('文章分析页面跳转失败 - 绝对路径:', err);
              
              // 尝试相对路径
              wx.navigateTo({
                url: './article-analysis/article-analysis',
                success: () => {
                  console.log('文章分析页面跳转成功 - 相对路径');
                },
                fail: (err2) => {
                  console.error('文章分析页面跳转失败 - 相对路径:', err2);
                  
                  wx.showModal({
                    title: '跳转失败',
                    content: `页面跳转失败\n绝对路径错误: ${err.errMsg}\n相对路径错误: ${err2.errMsg}`,
                    showCancel: false
                  });
                }
              });
            }
          });
        } else {
          // 其他功能使用原有逻辑
          wx.navigateTo({
            url: selectedTool.route,
            fail: (err) => {
              console.error('跳转失败:', err);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      } else {
        wx.showToast({
          title: '功能页面配置错误',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('工具选择出错:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },


}); 