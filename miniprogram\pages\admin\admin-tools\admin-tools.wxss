.container {
  padding: 32rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* 页面头部 */
.header {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(79, 70, 229, 0.3);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.header-icon {
  font-size: 48rpx;
  color: white;
}

.header-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  color: white;
}

.header-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 菜单区域 */
.menu-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.menu-group {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.menu-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #64748b;
  padding: 24rpx 32rpx 16rpx;
  background: #f8fafc;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 28rpx 32rpx;
  transition: all 0.3s ease;
  position: relative;
}

.menu-item:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 96rpx;
  right: 32rpx;
  height: 1rpx;
  background: #e2e8f0;
}

.menu-item:active {
  background: #f1f5f9;
  transform: scale(0.98);
}

.menu-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
}

.menu-icon-wrapper.blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.menu-icon-wrapper.orange {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.menu-icon {
  font-size: 32rpx;
  color: white;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.menu-title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
}

.menu-desc {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
}

.unread-hint {
  color: #ef4444;
  font-weight: 500;
}

.menu-arrow {
  font-size: 32rpx;
  color: #cbd5e1;
  font-weight: 300;
}

/* 角标样式 */
.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 36rpx;
  height: 36rpx;
  background: #ef4444;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid white;
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.4);
}

.badge-text {
  font-size: 20rpx;
  color: white;
  font-weight: 700;
  line-height: 1;
  padding: 0 6rpx;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 24rpx;
  padding: 24rpx 32rpx;
}

.quick-action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 16rpx;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.quick-action-item:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

.quick-icon {
  font-size: 32rpx;
  position: relative;
}

.quick-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 24rpx;
  height: 24rpx;
  background: #ef4444;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-badge-text {
  font-size: 16rpx;
  color: white;
  font-weight: 600;
  padding: 0 4rpx;
}

.quick-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
}

/* 状态信息 */
.status-section {
  margin-top: 32rpx;
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.status-item:not(:last-child) {
  border-bottom: 1rpx solid #e2e8f0;
}

.status-label {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
}

.status-value {
  font-size: 26rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.status-value.success {
  color: #16a34a;
  background: #dcfce7;
}

.status-value.warning {
  color: #ea580c;
  background: #fed7aa;
}

.status-value.running {
  color: #16a34a;
  background: #dcfce7;
} 