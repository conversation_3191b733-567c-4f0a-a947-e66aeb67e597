<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">教师工具箱</view>
    <view class="subtitle">专业教学辅助工具集</view>
  </view>



  <!-- 工具卡片列表 -->
  <view class="tools-container">
    <view class="section-title">可用工具</view>
    <view class="tools-grid">
      <view 
        class="tool-card {{item.developing ? 'disabled' : ''}}"
        wx:for="{{tools}}"
        wx:for-item="item"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onToolSelect"
      >
        <view class="card-header {{item.color}}">
          <text class="tool-icon">{{item.icon}}</text>
          <view class="tool-info">
            <text class="tool-title">{{item.title}}</text>
            <text class="tool-subtitle">{{item.subtitle}}</text>
          </view>
          <view class="developing-badge" wx:if="{{item.developing}}">开发中</view>
        </view>
        
        <view class="card-content">
          <text class="tool-description">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用贴士 -->
  <view class="tips-section">
    <view class="section-title">使用贴士</view>
    <view class="tips-container">
      <view class="tip-item">
        <text class="tip-dot">📊</text>
        <text class="tip-text">建议定期使用试卷分析功能了解学生学习情况，生成详细的成绩报告</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">👨‍🎓</text>
        <text class="tip-text">学生管理功能可以记录学生信息和成绩变化，帮助您掌握班级整体学习状况</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">🎤</text>
        <text class="tip-text">文本转语音支持多种语音选择，可调节语速音调，生成专业音频文件</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">📝</text>
        <text class="tip-text">使用文章词汇识别标记功能，快速分析英语文章的词汇难度和分布</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">💡</text>
        <text class="tip-text">支持高考3500词、四级词汇、六级词汇三种词库的智能识别</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">📱</text>
        <text class="tip-text">如有功能建议，欢迎通过意见反馈告诉我们</text>
      </view>
    </view>
  </view>
</view> 