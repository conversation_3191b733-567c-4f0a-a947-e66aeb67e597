const app = getApp();
const VIP_CONFIG = require('../../utils/vip-config.js');

Page({
  data: {
    userInfo: {
      nickName: '学习者',
      avatarUrl: '/assets/icons/profile.png'
    },
    userNickname: 'G', // 用户昵称首字母
    currentLibrary: {
      id: '3500',
      name: '3500大纲词汇'
    },
    plan: {
      dailyWords: 20,
      todayLearned: 5
    },
    planProgress: 25,
    todayLearned: 5,
    canLearn: true,
    canReview: true,
    stats: {
      totalWords: 156,
      masteredWords: 89,
      correctRate: 85,
      totalTime: 1200
    },
    records: [
      { date: '今天', words: 5, time: 15 },
      { date: '昨天', words: 20, time: 45 },
      { date: '前天', words: 18, time: 38 }
    ],
    isLoggedIn: false,
    showVipFeatures: VIP_CONFIG.enabled, // 是否显示VIP功能
    todayWords: 0,
    todayMinutes: 0,
    consecutiveDays: 0,
    registerDays: 1,
    userStats: {
      totalWords: 0,
      masteredWords: 0,
      totalTime: 0
    },
    noticeText: '各类词库正在逐步上传完善中，功能需求和bug欢迎通过"帮助与反馈"告诉我们',
    greetingFontSize: 26 // 动态字体大小，降低默认值
  },

  onLoad: function() {
    console.log('=== 首页加载 ===');
    console.log('data:', this.data);
    
    // 初始化页面数据
    this.initPageData();
    
    // 初始化用户信息和统计数据
    this.initUserInfo();
    this.loadTodayStats();
    
    // 计算注册天数
    this.calculateRegisterDays();
    
    // 加载通知内容
    this.loadNotice();
  },

  // 显示短链接输入框
  showShortLinkInput() {
    wx.showModal({
      title: '短链接访问',
      content: '请输入8位短链接ID',
      editable: true,
      placeholderText: '例如: abc123XY',
      success: (res) => {
        if (res.confirm && res.content) {
          const shortId = res.content.trim();
          if (shortId.length === 8) {
            wx.navigateTo({
              url: `/pages/shortlink/shortlink?id=${shortId}`
            });
          } else {
            wx.showToast({
              title: '请输入8位短链接ID',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 页面显示时触发
   */
  onShow() {
    console.log('=== 首页显示 ===');
    
    // 重新检查登录状态和加载数据
    this.loadUserData();
    
    // 检查是否有从词库页面选择的词库
    const app = getApp();
    if (app.globalData.selectedLibrary) {
      // 更新页面显示的当前词库
      this.setData({
        currentLibrary: app.globalData.selectedLibrary
      });
      // 清除全局数据
      app.globalData.selectedLibrary = null;
    }
    
    // 每次显示页面时刷新数据
    this.refreshData();
    
    // 刷新今日统计数据
    this.loadTodayStats();
    
    // 计算注册天数
    this.calculateRegisterDays();
    
    // 设置自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
  },

  // 初始化页面数据
  initPageData() {
    try {
      // 尝试加载用户信息和学习数据
      this.loadUserData();
      this.updateProgress();
    } catch (error) {
      console.error('初始化数据失败:', error);
    }
  },

  // 加载用户数据
  async loadUserData() {
    try {
      console.log('=== 首页加载用户数据 ===');
      
      // 获取app实例
      const app = getApp();
      
      // 强制重新检查登录状态
      app.checkLoginStatus();
      
      // 检查登录状态
      const isLoggedIn = app.isLoggedIn();
      console.log('登录状态:', isLoggedIn);
      
      const userInfo = await app.getUserInfo();
      console.log('获取到的用户信息:', userInfo);
      
      // 确保 userInfo.stats 对象存在
      if (userInfo && !userInfo.stats) {
        userInfo.stats = {
          totalWords: 0,
          continuousDays: 0,
          masteredWords: 0
        };
      }
      
      // 计算用户昵称首字母
      const nickname = userInfo?.wechatInfo?.nickName || userInfo?.username || '学习者';
      const userNickname = nickname.charAt(0).toUpperCase();
      
      // 设置用户信息到页面数据中
      this.setData({
        userInfo,
        isLoggedIn,
        userNickname
      });

      // 根据登录状态重新计算问候语和字体大小
      if (isLoggedIn && userInfo && userInfo._id) {
        console.log('用户已登录，显示个性化内容');
        // 登录用户重新计算注册天数
        this.calculateRegisterDays();
      } else {
        console.log('用户未登录，显示默认内容');
        // 未登录用户显示欢迎信息
        const fontSize = this.calculateGreetingFontSize(null);
        this.setData({
          registerDays: null,
          registerDaysText: null,
          greetingFontSize: fontSize
        });
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
             // 出错时显示默认数据
       const defaultUserInfo = {
         _id: null,
         username: '未登录用户',
         wechatInfo: {
           nickName: '未登录用户',
           avatarUrl: '/assets/icons/profile.png'
         },
         stats: {
           totalWords: 0,
           continuousDays: 0,
           masteredWords: 0
         }
       };
       this.setData({
         userInfo: defaultUserInfo,
         isLoggedIn: false,
         userNickname: '未',
         registerDays: null,
         registerDaysText: null,
         greetingFontSize: this.calculateGreetingFontSize(null)
       });
    }
  },

  // 更新学习进度
  updateProgress() {
    const { todayLearned, plan } = this.data;
    const progress = Math.min(Math.round((todayLearned / plan.dailyWords) * 100), 100);
    
    this.setData({
      planProgress: progress
    });
  },

  // 刷新页面数据
  refreshData() {
    console.log('刷新页面数据');
    this.loadUserData();
    this.updateProgress();
  },

  // 开始学习
  startLearning() {
    // 检查是否登录
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 检查是否能学习新单词
    if (!this.data.canLearn) {
      wx.showToast({
        title: '今日学习任务已完成',
        icon: 'success'
      });
      return;
    }

    // 跳转到学习页面
    wx.navigateTo({
      url: '/pages/learning/learning'
    });
  },

  // 开始复习
  startReview() {
    // 检查是否登录
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 检查是否有单词需要复习
    if (!this.data.canReview) {
      wx.showToast({
        title: '暂无单词需要复习',
        icon: 'none'
      });
      return;
    }

    // 跳转到复习页面
    wx.navigateTo({
      url: '/pages/review/review'
    });
  },

  // 处理功能卡片点击
  onFunctionTap(e) {
    const id = e.currentTarget.dataset.id;
    
    // 检查是否为VIP功能且用户没有VIP权限
    if (id !== 'teacher-tools' && VIP_CONFIG.isVipFunction(id)) {
      const userInfo = this.data.userInfo;
      if (!userInfo || !userInfo.isVip) {
        wx.showModal({
          title: '会员功能',
          content: '此功能需要开通会员才能使用，是否前往开通？',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/vip/vip'
              });
            }
          }
        });
        return;
      }
    }
    
    // 根据功能ID跳转到对应页面
    switch(id) {
      case 'wordtest':
        this.navigateToWordtest();
        break;
      case 'phrasetest':
        wx.navigateTo({
          url: '/pages/phrasetest/phrasetest'
        });
        break;
      case 'competition':
        this.navigateToCompetition();
        break;
      case 'writing':
        wx.navigateTo({
          url: '/pages/writing/writing'
        });
        break;
      case 'listening':
        this.navigateToListening();
        break;
      case 'teacher-tools':
        this.navigateToTeacherTools();
        break;
      case 'reading':
        this.navigateToReading();
        break;
      case 'mistakes':
        this.navigateToMistakes();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  // 切换词库
  onSwitchLibrary() {
    wx.navigateTo({
      url: '/pages/wordbank/library-selector/library-selector'
    });
  },

  // 点击促销横幅
  onPromoBannerTap() {
    // 如果启用了VIP功能，跳转到VIP页面
    if (VIP_CONFIG.enabled) {
      wx.navigateTo({
        url: '/pages/vip/vip'
      });
    } else {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    }
  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 师生互动学习空间',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-logo.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '墨词自习室 - 师生互动学习空间',
      query: '',
      imageUrl: '/assets/images/share-logo.png'
    };
  },

  // 跳转到个人中心
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  // 跳转到词库
  goToWordbank() {
    wx.switchTab({
      url: '/pages/wordbank/wordbank'
    });
  },

  // 跳转到错题本
  goToMistakes() {
    wx.navigateTo({
      url: '/pages/mistakes/mistakes'
    });
  },

  // 跳转到帮助
  goToHelp() {
    wx.navigateTo({
      url: '/pages/profile/help/help'
    });
  },

  // 跳转到关于
  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  // 初始化用户信息
  initUserInfo() {
    // 这里可以添加初始化用户信息的逻辑
  },

  // 加载今日统计数据 - 简化版，不查询数据库
  async loadTodayStats() {
    try {
      // 从本地存储获取今日学习数据
      const todayData = wx.getStorageSync('todayStats') || {};
      const today = new Date().toDateString();
      
      if (todayData.date === today) {
        this.setData({
          todayWords: todayData.words || 0,
          todayMinutes: Math.round((todayData.time || 0) / 60000) || 0,
          consecutiveDays: todayData.consecutiveDays || 0,
          userStats: {
            totalWords: 0,
            masteredWords: 0,
            totalTime: 0
          }
        });
      } else {
        // 如果不是今天的数据，重置
        this.setData({
          todayWords: 0,
          todayMinutes: 0,
          consecutiveDays: 0,
          userStats: {
            totalWords: 0,
            masteredWords: 0,
            totalTime: 0
          }
        });
      }
    } catch (error) {
      console.error('加载今日统计数据失败:', error);
      this.setData({
        todayWords: 0,
        todayMinutes: 0,
        consecutiveDays: 0,
        userStats: {
          totalWords: 0,
          masteredWords: 0,
          totalTime: 0
        }
      });
    }
  },

  // 计算连续学习天数 - 简化版，使用本地存储
  async calculateConsecutiveDays() {
    try {
      // 从本地存储获取连续学习天数
      const streakData = wx.getStorageSync('learningStreak') || { days: 0, lastDate: null };
      return streakData.days || 0;
    } catch (error) {
      console.error('计算连续学习天数失败:', error);
      return 0;
    }
  },

  // 获取用户学习统计 - 简化版，返回默认值
  async getUserLearningStats() {
    try {
      // 从本地存储获取统计数据
      const statsData = wx.getStorageSync('userStats') || {
        totalWords: 0,
        masteredWords: 0,
        totalTime: 0
      };
      return statsData;
    } catch (error) {
      console.error('获取用户学习统计失败:', error);
      return {
        totalWords: 0,
        masteredWords: 0,
        totalTime: 0
      };
    }
  },

  // 导航到单词检测
  navigateToWordtest() {
    wx.navigateTo({
      url: '/pages/wordtest/wordtest'
    });
  },

  // 导航到词库学习
  navigateToWordbank() {
    wx.switchTab({
      url: '/pages/wordbank/wordbank'
    });
  },

  // 导航到听力训练
  navigateToListening() {
    wx.navigateTo({
      url: '/pages/listening/listening'
    });
  },

  // 导航到听写模式
  navigateToDictation() {
    // 设置returnTo参数，用于wordbank页面识别来源
    wx.switchTab({
      url: '/pages/wordbank/wordbank'
    });
    
    // 通过全局数据传递来源信息
    const app = getApp();
    app.globalData.returnTo = 'dictation';
  },

  // 导航到竞赛模式
  navigateToCompetition() {
    wx.navigateTo({
      url: '/pages/competition/competition'
    });
  },

  // 导航到错题本
  navigateToMistakes() {
    wx.navigateTo({
      url: '/pages/mistakes/mistakes'
    });
  },

  // 导航到阅读材料
  navigateToReading() {
    wx.navigateTo({
      url: '/pages/reading/reading'
    });
  },

  // 导航到教师专区
  navigateToTeacherTools() {
    wx.navigateTo({
      url: '/pages/teacher-tools/teacher-tools'
    });
  },

  // 导航到个人中心
  navigateToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  // 计算注册天数
  async calculateRegisterDays() {
    try {
      // 检查用户是否已登录
      if (!this.data.isLoggedIn) {
        console.log('用户未登录，不显示注册天数');
        // 未登录用户显示欢迎信息
        this.setData({
          registerDays: null,
          registerDaysText: null,
          greetingFontSize: 26
        });
        return;
      }

      // 获取用户信息，根据用户的实际注册时间计算
      const userInfo = this.data.userInfo;
      if (!userInfo || !userInfo.createTime) {
        console.log('用户信息不完整，使用默认天数');
        this.setData({
          registerDays: 1,
          registerDaysText: '1天',
          greetingFontSize: 26
        });
        return;
      }

      // 计算从注册日期到今天的天数
      const today = new Date();
      const createTime = new Date(userInfo.createTime);
      const timeDiff = today.getTime() - createTime.getTime();
      const daysDiff = Math.max(1, Math.ceil(timeDiff / (1000 * 3600 * 24))); // 至少1天

      console.log('用户注册时间:', createTime, '今天:', today, '注册天数:', daysDiff);

      // 格式化显示天数
      let formattedDays = this.formatDaysDisplay(daysDiff);

      // 检查文本长度，如果太长则使用紧凑模式
      const nickname = userInfo?.wechatInfo?.nickName || userInfo?.username || '学习者';
      const fullText = `${nickname}，今天是您使用墨词自习室的第${formattedDays}`;

      if (fullText.length > 26) {
        // 文本太长，使用紧凑模式（降低阈值）
        formattedDays = this.formatDaysDisplay(daysDiff, true);
      }

      // 计算动态字体大小
      const fontSize = this.calculateGreetingFontSize(formattedDays);

      this.setData({
        registerDays: daysDiff,
        registerDaysText: formattedDays,
        greetingFontSize: fontSize
      });

    } catch (error) {
      console.error('计算注册天数失败:', error);
      // 如果是登录用户但计算失败，显示默认值
      if (this.data.isLoggedIn) {
        this.setData({
          registerDays: 1,
          registerDaysText: '1天',
          greetingFontSize: 26
        });
      } else {
        // 未登录用户不显示天数
        this.setData({
          registerDays: null,
          registerDaysText: null,
          greetingFontSize: 26
        });
      }
    }
  },

  // 格式化天数显示
  formatDaysDisplay(days, compact = false) {
    if (days >= 365) {
      const years = Math.floor(days / 365);
      const remainingDays = days % 365;
      if (remainingDays === 0) {
        return `${years}年`;
      } else if (remainingDays >= 30) {
        const months = Math.floor(remainingDays / 30);
        const finalDays = remainingDays % 30;
        if (finalDays === 0) {
          return `${years}年${months}个月`;
        } else if (compact) {
          // 紧凑模式：1年1月5天
          return `${years}年${months}月${finalDays}天`;
        } else if (finalDays === 1) {
          return `${years}年${months}个月零1天`;
        } else {
          return `${years}年${months}个月零${finalDays}天`;
        }
      } else if (compact) {
        // 紧凑模式：1年5天
        return `${years}年${remainingDays}天`;
      } else if (remainingDays === 1) {
        return `${years}年零1天`;
      } else {
        return `${years}年零${remainingDays}天`;
      }
    } else if (days >= 30) {
      const months = Math.floor(days / 30);
      const remainingDays = days % 30;
      if (remainingDays === 0) {
        return `${months}个月`;
      } else if (compact) {
        // 紧凑模式：1月5天
        return `${months}月${remainingDays}天`;
      } else if (remainingDays === 1) {
        return `${months}个月零1天`;
      } else {
        return `${months}个月零${remainingDays}天`;
      }
    } else {
      return `${days}天`;
    }
  },

  // 计算问候语字体大小
  calculateGreetingFontSize(daysText) {
    // 获取用户昵称
    const nickname = this.data.userInfo?.wechatInfo?.nickName || this.data.userInfo?.username || '学习者';

    // 构建完整的问候语文本
    let fullText;
    if (this.data.isLoggedIn && daysText) {
      fullText = `${nickname}，今天是您使用墨词自习室的第${daysText}`;
    } else {
      fullText = `${nickname}，欢迎使用墨词自习室`;
    }

    // 根据文本长度动态调整字体大小
    const textLength = fullText.length;

    // 获取系统信息来判断屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.screenWidth;

    let baseFontSize = 26; // 默认字体大小，降低基础大小

    // 根据屏幕宽度调整基础字体大小
    if (screenWidth <= 320) {
      baseFontSize = 20; // 小屏幕
    } else if (screenWidth <= 375) {
      baseFontSize = 22; // 中等屏幕
    } else {
      baseFontSize = 26; // 大屏幕，降低大小
    }

    // 根据文本长度进一步调整
    if (textLength > 28) {
      baseFontSize = Math.max(baseFontSize - 6, 16); // 很长文本，最小16rpx
    } else if (textLength > 24) {
      baseFontSize = Math.max(baseFontSize - 4, 18); // 较长文本，最小18rpx
    } else if (textLength > 20) {
      baseFontSize = Math.max(baseFontSize - 2, 20); // 中等文本，最小20rpx
    }

    return baseFontSize;
  },

  // 加载通知内容
  async loadNotice() {
    try {
      console.log('开始加载通知内容...');
      
      const result = await wx.cloud.callFunction({
        name: 'getNotice'
      });

      if (result.result && result.result.success && result.result.data) {
        console.log('通知加载成功:', result.result.data);
        this.setData({
          noticeText: result.result.data.content
        });
      } else {
        console.log('使用默认通知内容');
        // 保持默认通知内容
      }
    } catch (error) {
      console.error('加载通知失败:', error);
      // 发生错误时保持默认通知内容
    }
  }
}); 