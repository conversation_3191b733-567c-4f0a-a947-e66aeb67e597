/* 学生管理页面样式 */
.container {
  padding: 20rpx;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 统计卡片 */
.stats-container {
  margin-bottom: 30rpx;
}

.stats-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  min-height: 180rpx; /* 固定卡片最小高度 */
  align-items: center; /* 垂直居中对齐 */
}

.stat-item {
  text-align: center;
  flex: 1;
  min-height: 120rpx; /* 固定最小高度，防止布局跳动 */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #4F46E5;
  margin-bottom: 8rpx;
  min-height: 60rpx; /* 固定数字区域高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-number.green {
  color: #10B981;
}

.stat-number.red {
  color: #EF4444;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  min-height: 32rpx; /* 固定标签高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  gap: 16rpx;
}





.add-btn {
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border-radius: 20rpx;
  background: #10B981 !important;
  color: white !important;
  border: none;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 120rpx;
  display: block;
}

.manage-btn {
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border-radius: 20rpx;
  background: #4F46E5 !important;
  color: white !important;
  border: none;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 120rpx;
  display: block;
}

.manage-btn.active {
  background: #EF4444 !important;
  color: white !important;
}

/* 批量操作 */
.batch-actions {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  font-size: 26rpx;
  color: #856404;
  font-weight: 500;
}

.batch-delete-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
  background: #dc3545;
  color: white;
  border: none;
}

/* 学生列表 */
.students-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.student-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 学生内容容器 */
.student-content {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  width: 100%;
}

/* 复选框样式 */
.checkbox-container {
  flex-shrink: 0;
  margin-top: 8rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #d1d5db;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #10B981;
  border-color: #10B981;
}

.checkmark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.student-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex: 1;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.student-details {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.student-stats {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.score-info {
  text-align: right;
  font-size: 24rpx;
}

.avg-score {
  color: #4F46E5;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.recent-score {
  color: #666;
  margin-bottom: 4rpx;
}

.score-count {
  color: #999;
  font-size: 22rpx;
}

.trend-indicator {
  font-size: 32rpx;
}

.trend-icon.up {
  color: #10B981;
}

.trend-icon.down {
  color: #EF4444;
}

.trend-icon {
  color: #666;
}

.student-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border-radius: 20rpx;
  border: none;
}

.action-btn.primary {
  background: #4F46E5;
  color: white;
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #666;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #666;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  box-sizing: border-box;
  color: #333;
  line-height: 1.5;
  min-height: 80rpx;
  display: block;
}

.form-input.picker-input {
  position: relative;
  display: flex;
  align-items: center;
  color: #333;
  cursor: pointer;
  height: 80rpx;
  padding: 0 50rpx 0 24rpx;
  box-sizing: border-box;
}

.picker-input text {
  font-size: 28rpx;
  line-height: 1;
  display: block;
  transform: translateY(-12rpx);
}

.placeholder-text {
  color: #999;
  font-size: 28rpx;
  line-height: 1;
}

.picker-arrow {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 20rpx;
  line-height: 1;
}

.input-placeholder {
  color: #999 !important;
  font-size: 28rpx !important;
  opacity: 1;
}

.form-input:focus {
  border-color: #4F46E5;
  background: white;
}

.input-container {
  position: relative;
  width: 100%;
}

.picker-container {
  position: relative;
  width: 100%;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 2rpx solid #f3f4f6;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f3f4f6;
  color: #666;
}

.modal-btn.confirm {
  background: #4F46E5;
  color: white;
}
