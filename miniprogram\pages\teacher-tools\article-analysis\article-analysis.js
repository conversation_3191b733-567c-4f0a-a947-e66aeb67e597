Page({
  data: {
    currentStep: 0,
    articleText: '',
    analyzedArticle: null,
    selectedVocabTypes: ['gaokao'], // 默认只选中高考
    vocabTypes: [
      {
        id: 'gaokao',
        name: '高考',
        description: '3500大纲词汇',
        color: '#ff6b6b',
        collection: 'words_3500'
      },
      {
        id: 'cet4',
        name: '四级',
        description: '四级词汇',
        color: '#4ecdc4',
        collection: 'words_siji'
      },
      {
        id: 'cet6',
        name: '六级',
        description: '六级词汇',
        color: '#45b7d1',
        collection: 'words_liuji'
      }
    ],
    vocabCache: {},
    loading: false,
    stats: {
      totalWords: 0,
      markedWords: 0,
      uniqueWords: 0,
      coverage: 0,
      // 新增各词库的统计
      gaokaoWords: 0,
      cet4Words: 0,
      cet6Words: 0
    },
    // 复选框选中状态（解决WXML数组方法兼容性问题）
    gaokaoSelected: true,
    cet4Selected: false,
    cet6Selected: false,
    // 动态画布高度
    canvasHeight: 6000
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '文章词汇识别标记'
    });
    
    // 初始化真实词库数据
    this.initVocabData();
  },

  // 初始化真实词库数据
  async initVocabData() {
    console.log('初始化词库数据...');
    wx.showLoading({ title: '加载词库中...' });

    try {
      const vocabCache = {};

      // 词库ID映射
      const libraryMapping = {
        'gaokao': 'gaokao_3500',    // 高考3500词
        'cet4': 'college_cet4',     // 四级词汇
        'cet6': 'college_cet6'      // 六级词汇
      };

      // 并行加载所有词库
      const loadPromises = this.data.vocabTypes.map(async (vocabType) => {
        const libraryId = libraryMapping[vocabType.id];
        if (!libraryId) return;

        try {


          // 分批加载完整词库
          const allWords = await this.loadCompleteVocab(libraryId, vocabType.name);

          if (allWords.length > 0) {
            const wordSet = new Set();

            // 将词汇添加到Set中（转为小写）
            allWords.forEach(wordObj => {
              // 兼容不同的字段名：word 或 words
              const wordText = wordObj.word || wordObj.words;
              if (wordText) {
                wordSet.add(wordText.toLowerCase().trim());
              }
            });

            vocabCache[vocabType.id] = wordSet;
          } else {
            vocabCache[vocabType.id] = new Set(); // 空词库
          }
        } catch (error) {
          vocabCache[vocabType.id] = new Set(); // 空词库
        }
      });

      await Promise.all(loadPromises);

      this.setData({
        vocabCache: vocabCache
      });

      wx.hideLoading();
      console.log('所有词库加载完成');

    } catch (error) {
      wx.hideLoading();
      console.error('词库初始化失败:', error);
      wx.showToast({
        title: '词库加载失败',
        icon: 'none'
      });
    }
  },

  // 分批加载完整词库
  async loadCompleteVocab(libraryId, vocabName) {
    try {
      // 先获取总数
      const countResult = await wx.cloud.callFunction({
        name: 'getWords',
        data: {
          libraryId: libraryId,
          limit: 1,
          getTotalCount: true
        }
      });

      if (!countResult.result || countResult.result.code !== 200) {
        throw new Error('获取词库总数失败');
      }

      const totalCount = countResult.result.totalCount || 0;

      if (totalCount === 0) {
        return [];
      }

      // 分批加载，每批1000个（云数据库限制）
      const batchSize = 1000;
      const totalBatches = Math.ceil(totalCount / batchSize);
      const allWords = [];

      for (let i = 0; i < totalBatches; i++) {
        const skip = i * batchSize;
        const limit = Math.min(batchSize, totalCount - skip);

        const batchResult = await wx.cloud.callFunction({
          name: 'getWords',
          data: {
            libraryId: libraryId,
            skip: skip,
            limit: limit
          }
        });

        if (batchResult.result && batchResult.result.code === 200) {
          const batchWords = batchResult.result.data || [];
          allWords.push(...batchWords);
        }
      }
      return allWords;

    } catch (error) {
      return [];
    }
  },

  onArticleInput(e) {
    this.setData({
      articleText: e.detail.value
    });
  },

  clearArticle() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空当前输入的文章内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            articleText: '',
            currentStep: 0,
            analyzedArticle: null
          });
        }
      }
    });
  },

  pasteFromClipboard() {
    wx.getClipboardData({
      success: (res) => {
        const clipboardText = res.data;
        if (clipboardText && clipboardText.trim()) {
          this.setData({
            articleText: clipboardText.trim()
          });
          wx.showToast({
            title: '粘贴成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '剪贴板为空',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '粘贴失败',
          icon: 'none'
        });
      }
    });
  },

  loadSampleArticle() {
    const samples = [
      {
        title: '哲学思辨',
        content: `If you find yourself with several million dollars more than you need, how should you spend the money? One answer might be to do whatever you want, within the bounds of the law. Another is to donate it to a charity. You may also support an organization that is working to reduce existential risks.
      The history of philosophy consists of attempts to shed light on such questions. However, philosophy's open secret is that these attempts don't add up to anything decisive. When it comes to what we ought to do in any given situation, different ethical systems offer different guidance. Conflict is baked into questions like, "What ought I to do?"
      Or is it? An awareness of difference is certainly crucial to such an inquiry. But this needn't become a conflict until you're forced to pick a side. Some philosophers have sought to eliminate the conflict between ethical systems. Derek Parfit, one of the most respected philosophers of the past fifty years, devoted the second half of his life to precisely this task.
      Unfortunately, the position that different ethical positions can be reconciled (和解) is itself a position others may not accept. Parfit believed people could have a good reason to act morally,independent of their knowledge or beliefs. If you find yourself in a position to alleviate (减轻) others' suffering without significantly inconveniencing yourself, then you should act. It is just a moral fact that there is a right thing that you ought to do. By contrast, Bernard Williams argued it made no sense to talk about people's reasons independently from their motivations. Someone cannot have a reason to do something that they have no desire to do-because however wrong-headed their preferences may be, subjectivity is the ground truth for "having a reason".
      As in Peter Singer's parable (寓言) of a child drowning in a shallow pond, it self-evidently seems immoral not to save this child if the only cost for you is a pair of new trainers. This is as clear as a philosophical argument can get. However, as with all moral reasoning, you are free to reject the logic or assumptions behind Singer's argument, regardless of its clarity.
      Parfit was a philosopher's philosopher. Yet he suffered from the fact that irreconcilable ethical systems exist. Why? Because one conclusion that follows from this is that, if the differences between such systems cannot be resolved by philosophical means, conflict of a literal kind will always exist beneath their differences.
      As Karl Popper puts it, " If we extend unlimited tolerance even to those who are intolerant, then the tolerant will be destroyed, and tolerance with them." This is the rub. Philosophy is of the world as well as of the page-and even the gentlest words may, sooner or later, need an army to defend them. "We should therefore claim," Popper continued, "in the name of the tolerance, the right not to tolerate the intolerant."
      I'm not surprised that Parfit felt despair at the limits of his persuasiveness. What's amazing is that more philosophers don't feel the same way.`
      }
    ];

    const randomSample = samples[Math.floor(Math.random() * samples.length)];
    
    wx.showModal({
      title: '加载示例文章',
      content: `将加载「${randomSample.title}」示例文章，是否继续？`,
      success: (res) => {
        if (res.confirm) {
          this.setData({
            articleText: randomSample.content
          });
          
          wx.showToast({
            title: `已加载「${randomSample.title}」`,
            icon: 'success'
          });
        }
      }
    });
  },

  async startAnalysis() {
    if (!this.data.articleText.trim()) {
      wx.showToast({
        title: '请先输入文章内容',
        icon: 'none'
      });
      return;
    }

    // 检查词库是否已加载
    const { selectedVocabTypes, vocabCache } = this.data;
    for (const vocabType of selectedVocabTypes) {
      if (!vocabCache[vocabType] || vocabCache[vocabType].size === 0) {
        wx.showToast({
          title: '词库正在加载中，请稍后重试',
          icon: 'none'
        });
        return;
      }
    }

    this.setData({ loading: true });

    try {
      const analyzedData = this.analyzeArticle(this.data.articleText);

      this.setData({
        analyzedArticle: analyzedData,
        currentStep: 1,
        loading: false
      });

      this.calculateStats();

    } catch (error) {
      console.error('分析文章失败:', error);
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },





  analyzeArticle(text) {
    const { selectedVocabTypes, vocabCache } = this.data;

    // 检查所有选中的词库是否已加载
    for (const vocabType of selectedVocabTypes) {
      if (!vocabCache[vocabType]) {
        throw new Error(`词库数据未加载: ${vocabType}`);
      }
    }

    const words = this.extractWords(text);
    const markedText = this.markMultipleVocabularies(text, selectedVocabTypes, vocabCache);

    return {
      originalText: text,
      markedText: markedText,
      words: words,
      vocabTypes: selectedVocabTypes
    };
  },

  extractWords(text) {
    // 更精确的英文单词提取正则表达式
    const wordRegex = /\b[a-zA-Z]{2,}\b/g; // 至少2个字母的英文单词
    const matches = text.match(wordRegex) || [];
    return matches.map(word => word.toLowerCase().trim());
  },

  // 多词库标记方法 - 支持重合词汇显示
  markMultipleVocabularies(text, selectedVocabTypes, vocabCache) {
    const { vocabTypes } = this.data;

    return text.replace(/\b[a-zA-Z]{2,}\b/g, (match) => {
      const lowerWord = match.toLowerCase().trim();

      // 检查该词汇属于哪些词库
      const belongsTo = [];
      selectedVocabTypes.forEach(vocabType => {
        if (vocabCache[vocabType]?.has(lowerWord)) {
          belongsTo.push(vocabType);
        }
      });

      if (belongsTo.length === 0) {
        return match; // 不属于任何选中的词库
      }

      // 如果只属于一个词库，使用单一标记
      if (belongsTo.length === 1) {
        const vocabType = belongsTo[0];
        const vocabConfig = vocabTypes.find(v => v.id === vocabType);
        return `<span class="marked-word marked-${vocabType}" title="${vocabConfig?.name || '词汇'}词汇: ${match}">${match}</span>`;
      }

      // 如果属于多个词库，使用组合标记
      const vocabNames = belongsTo.map(type => {
        const vocab = vocabTypes.find(v => v.id === type);
        return vocab ? vocab.name : type;
      }).join('+');

      // 按优先级选择主要样式：高考 > 六级 > 四级
      const priorityOrder = ['gaokao', 'cet6', 'cet4'];
      const primaryType = priorityOrder.find(type => belongsTo.includes(type)) || belongsTo[0];

      return `<span class="marked-word marked-${primaryType} marked-multiple" data-vocabs="${belongsTo.join(',')}" title="${vocabNames}词汇: ${match}">${match}</span>`;
    });
  },

  // 保留原方法以备用
  markVocabulary(text, vocabSet) {
    const { selectedVocabTypes, vocabTypes } = this.data;
    const selectedVocabType = selectedVocabTypes[0]; // 取第一个选中的词库
    const vocabConfig = vocabTypes.find(v => v.id === selectedVocabType);

    return text.replace(/\b[a-zA-Z]{2,}\b/g, (match) => {
      const lowerWord = match.toLowerCase().trim();
      if (vocabSet.has(lowerWord)) {
        // 创建更美观的标记效果
        return `<span class="marked-word marked-${selectedVocabType}" title="${vocabConfig?.name || '词汇'}词汇: ${match}">${match}</span>`;
      }
      return match;
    });
  },

  calculateStats() {
    const { analyzedArticle, selectedVocabTypes, vocabCache } = this.data;

    if (!analyzedArticle || selectedVocabTypes.length === 0) {
      return;
    }

    const words = analyzedArticle.words;
    const totalWords = words.length;
    const uniqueWords = [...new Set(words)].length;

    // 计算各词库的标记数量
    let gaokaoWords = 0;
    let cet4Words = 0;
    let cet6Words = 0;

    const markedWordsSet = new Set(); // 用于去重，避免重复计算

    words.forEach(word => {
      const lowerWord = word.toLowerCase();
      let isMarked = false;

      // 分别检查每个词库（允许重复计算）
      if (selectedVocabTypes.includes('gaokao') && vocabCache['gaokao']?.has(lowerWord)) {
        gaokaoWords++;
        isMarked = true;
      }
      if (selectedVocabTypes.includes('cet6') && vocabCache['cet6']?.has(lowerWord)) {
        cet6Words++;
        isMarked = true;
      }
      if (selectedVocabTypes.includes('cet4') && vocabCache['cet4']?.has(lowerWord)) {
        cet4Words++;
        isMarked = true;
      }

      if (isMarked) {
        markedWordsSet.add(lowerWord);
      }
    });

    const totalMarkedWords = words.filter(word => {
      const lowerWord = word.toLowerCase();
      return selectedVocabTypes.some(type => vocabCache[type]?.has(lowerWord));
    }).length;

    const coverage = totalWords > 0 ? Math.round((totalMarkedWords / totalWords) * 100) : 0;

    this.setData({
      stats: {
        totalWords,
        markedWords: totalMarkedWords,
        uniqueWords,
        coverage,
        gaokaoWords,
        cet4Words,
        cet6Words
      }
    });
  },

  // 切换词库选择（支持多选）
  toggleVocabType(e) {
    const vocabType = e.currentTarget.dataset.type;
    let { selectedVocabTypes } = this.data;

    if (selectedVocabTypes.includes(vocabType)) {
      // 如果已选中，则取消选择（但至少保留一个）
      if (selectedVocabTypes.length > 1) {
        selectedVocabTypes = selectedVocabTypes.filter(type => type !== vocabType);
      }
    } else {
      // 如果未选中，则添加选择
      selectedVocabTypes = [...selectedVocabTypes, vocabType];
    }

    // 更新选中状态的布尔值
    const updateData = {
      selectedVocabTypes: selectedVocabTypes,
      gaokaoSelected: selectedVocabTypes.includes('gaokao'),
      cet4Selected: selectedVocabTypes.includes('cet4'),
      cet6Selected: selectedVocabTypes.includes('cet6')
    };

    this.setData(updateData);

    // 如果有分析结果，重新分析和计算统计
    if (this.data.analyzedArticle) {
      this.reanalyzeWithNewVocabs();
    }
  },

  // 使用新的词库选择重新分析
  async reanalyzeWithNewVocabs() {
    const { articleText, selectedVocabTypes, vocabCache } = this.data;

    // 检查所有选中的词库是否已加载
    for (const vocabType of selectedVocabTypes) {
      if (!vocabCache[vocabType] || vocabCache[vocabType].size === 0) {
        wx.showToast({
          title: '词库正在加载中，请稍后重试',
          icon: 'none'
        });
        return;
      }
    }

    this.setData({ loading: true });

    try {
      // 重新分析文章
      const analyzedData = this.analyzeArticle(articleText);

      this.setData({
        analyzedArticle: analyzedData,
        loading: false
      });

      this.calculateStats();

    } catch (error) {
      console.error('重新分析失败:', error);
      wx.showToast({
        title: '分析失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 保留原方法以备用
  switchVocabType(e) {
    const vocabType = e.currentTarget.dataset.type;

    this.setData({
      selectedVocabTypes: [vocabType] // 改为数组格式
    });

    if (this.data.analyzedArticle) {
      this.reanalyzeWithNewVocabs();
    }
  },

  backToEdit() {
    this.setData({
      currentStep: 0,
      analyzedArticle: null
    });
  },

  copyResult() {
    if (!this.data.analyzedArticle) return;
    
    const plainText = this.data.analyzedArticle.markedText.replace(/<[^>]*>/g, '');
    
    wx.setClipboardData({
      data: plainText,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 保存到相册
  saveToAlbum() {
    if (!this.data.analyzedArticle) return;

    // 检查微信版本和环境
    const systemInfo = wx.getSystemInfoSync();
    console.log('保存图片 - 系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion
    });

    // 先尝试直接保存，如果失败再处理权限
    this.attemptDirectSave();
  },

  // 尝试直接保存（绕过权限检查）
  attemptDirectSave() {
    wx.showLoading({
      title: '正在生成图片...',
      mask: true
    });

    // 生成图片并保存到相册
    this.doSaveToAlbum();
  },

  // 处理保存失败的情况
  handleSaveFailure(err, tempFilePath) {
    console.error('保存图片失败详情:', err);

    if (err.errMsg.includes('privacy api banned') || err.errMsg.includes('banned')) {
      // API被禁用，提供替代方案
      this.showPrivacyApiBannedDialog();
    } else if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
      // 权限问题，尝试权限流程
      this.handlePermissionFlow(tempFilePath);
    } else {
      // 其他错误，显示通用错误处理
      this.showFallbackOptions();
    }
  },

  // 显示隐私API被禁用的对话框
  showPrivacyApiBannedDialog() {
    wx.showModal({
      title: '保存功能暂不可用',
      content: '由于微信政策限制，当前版本暂时无法直接保存图片到相册。\n\n建议您：\n1. 截屏保存当前页面\n2. 等待后续版本更新\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 提示用户截屏
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 处理权限流程
  handlePermissionFlow(tempFilePath) {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 用户开启了权限，重新尝试保存
                      this.retrySaveImage(tempFilePath);
                    }
                  }
                });
              }
            }
          });
        } else {
          // 尝试请求权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.retrySaveImage(tempFilePath);
            },
            fail: () => {
              this.showFallbackOptions();
            }
          });
        }
      },
      fail: () => {
        this.showFallbackOptions();
      }
    });
  },

  // 重新尝试保存图片
  retrySaveImage(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('重试保存失败:', err);
        this.showFallbackOptions();
      }
    });
  },

  // 显示备选方案
  showFallbackOptions() {
    wx.showModal({
      title: '保存失败',
      content: '图片保存失败，您可以：\n\n1. 截屏保存当前页面\n2. 稍后重试\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '稍后重试',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 执行保存到相册
  doSaveToAlbum() {
    wx.showLoading({
      title: '正在生成图片...',
      mask: true
    });

    console.log('开始生成文章标记图片...');
    this.generateMarkedArticleImage().then((canvasInfo) => {
      console.log('Canvas绘制完成，尺寸:', canvasInfo);
      // 增加延迟确保Canvas绘制完成
      setTimeout(() => {
        console.log('开始转换Canvas为图片...');
        wx.canvasToTempFilePath({
          canvasId: 'articlePdfCanvas',
          x: 0,
          y: 0,
          width: canvasInfo.width,
          height: canvasInfo.height,
          destWidth: Math.min(canvasInfo.width, 1500), // 限制最大宽度
          destHeight: Math.min(canvasInfo.height, 2000), // 限制最大高度
          fileType: 'jpg', // 使用jpg格式减小文件大小
          quality: 0.8, // 设置图片质量
          success: (res) => {
            console.log('Canvas转图片成功:', res.tempFilePath);
            wx.hideLoading();
            // 直接尝试保存，不预先检查权限
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '已保存到相册',
                  icon: 'success'
                });
              },
              fail: (err) => {
                console.error('直接保存失败:', err);
                // 如果直接保存失败，尝试权限处理流程
                this.handleSaveFailure(err, res.tempFilePath);
              }
            });
          },
          fail: (err) => {
            wx.hideLoading();
            console.error('生成图片失败:', err);
            wx.showModal({
              title: '生成失败',
              content: `图片生成失败：${err.errMsg || '未知错误'}`,
              showCancel: false,
              confirmText: '知道了'
            });
          }
        }, this);
      }, 2000); // 增加延迟时间，手机端需要更长时间
    }).catch((err) => {
      wx.hideLoading();
      console.error('Canvas绘制失败:', err);
      wx.showModal({
        title: '生成失败',
        content: `Canvas绘制失败：${err.message || '未知错误'}`,
        showCancel: false,
        confirmText: '知道了'
      });
    });
  },

  shareResult() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 生成带标记的文章图片
  generateMarkedArticleImage() {
    return new Promise((resolve, reject) => {
      const { analyzedArticle, stats, selectedVocabTypes, vocabTypes } = this.data;
      const vocabNames = selectedVocabTypes.map(type => {
        const vocab = vocabTypes.find(v => v.id === type);
        return vocab ? vocab.name : type;
      });
      const vocabName = vocabNames.length === 1 ? vocabNames[0] : vocabNames.join('+');

      const ctx = wx.createCanvasContext('articlePdfCanvas', this);
      const canvasWidth = 750;

      // 超保守估算：确保绝对足够的空间
      const plainText = analyzedArticle.markedText.replace(/<[^>]*>/g, '');
      const textLength = plainText.length;
      // 大幅增加高度：每个字符1.5px + 大量缓冲
      let canvasHeight = Math.max(5000, textLength * 1.5 + 2000);

      // 动态设置canvas高度
      this.setData({ canvasHeight: canvasHeight });

      // 设置背景
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // 标题
      ctx.setFillStyle('#333333');
      ctx.setFontSize(32);
      ctx.setTextAlign('center');
      ctx.fillText('文章词汇标记分析', canvasWidth / 2, 60);

      // 词汇类型
      ctx.setFontSize(24);
      ctx.fillText(`${vocabName}词汇标记`, canvasWidth / 2, 100);

      // 统计信息
      ctx.setTextAlign('left');
      ctx.setFontSize(20);
      let yPos = 150;

      ctx.fillText(`总词数: ${stats.totalWords}`, 50, yPos);
      yPos += 35;
      ctx.fillText(`标记词数: ${stats.markedWords}`, 50, yPos);
      yPos += 35;
      ctx.fillText(`覆盖率: ${stats.coverage}%`, 50, yPos);
      yPos += 50;

      // 添加颜色说明
      ctx.setFontSize(18);
      ctx.setFillStyle('#333333');
      ctx.fillText('颜色说明:', 50, yPos);
      yPos += 30;

      // 绘制颜色图例
      selectedVocabTypes.forEach((vocabTypeId, index) => {
        const vocabConfig = vocabTypes.find(v => v.id === vocabTypeId);
        if (vocabConfig) {
          // 绘制颜色方块
          ctx.setFillStyle(vocabConfig.color);
          ctx.fillRect(70, yPos - 15, 20, 20);

          // 绘制文字说明
          ctx.setFillStyle('#333333');
          ctx.setFontSize(16);
          ctx.fillText(`${vocabConfig.name}词汇`, 100, yPos);
          yPos += 25;
        }
      });

      // 重合词汇说明
      if (selectedVocabTypes.length > 1) {
        ctx.setFillStyle('#FF9800');
        ctx.fillRect(70, yPos - 15, 20, 20);
        ctx.setFillStyle('#333333');
        ctx.fillText('重合词汇（右上角有橙色标记）', 100, yPos);
        yPos += 25;
      }

      yPos += 20;

      // 分割线
      ctx.setStrokeStyle('#e9ecef');
      ctx.setLineWidth(2);
      ctx.beginPath();
      ctx.moveTo(50, yPos);
      ctx.lineTo(canvasWidth - 50, yPos);
      ctx.stroke();
      yPos += 30;

      // 渲染带标记的文章内容（多词库模式下使用第一个词库的样式）
      const primaryVocabType = selectedVocabTypes[0] || 'gaokao';
      yPos = this.renderMarkedText(ctx, analyzedArticle.markedText, 50, yPos, canvasWidth - 100, primaryVocabType);

      // 底部信息
      yPos += 50;
      ctx.setFontSize(16);
      ctx.setFillStyle('#666666');
      ctx.setTextAlign('center');
      ctx.fillText('墨词自习室 - 文章词汇分析', canvasWidth / 2, yPos);

      ctx.draw(false, () => {
        setTimeout(() => {
          // 使用实际内容高度，而不是预估的画布高度
          const actualHeight = yPos + 100; // 给一点底部边距

          resolve({
            width: canvasWidth,
            height: actualHeight, // 使用实际高度
            actualContentHeight: actualHeight
          });
        }, 500);
      });
    });
  },



  // 估算内容高度
  estimateContentHeight(markedText, maxWidth) {
    // 更准确的估算方法
    const words = this.parseMarkedTextToWords(markedText);
    const lineHeight = 35;
    const wordSpacing = 8;
    const lineSpacing = 12;
    const avgWordWidth = 60; // 平均单词宽度（包括标记样式）

    let currentLineWidth = 0;
    let totalLines = 1;

    words.forEach(wordInfo => {
      const { word } = wordInfo;
      // 估算单词宽度（英文字符约8px，加上内边距）
      const estimatedWordWidth = word.length * 8 + 16;

      if (currentLineWidth + estimatedWordWidth + wordSpacing > maxWidth && currentLineWidth > 0) {
        totalLines++;
        currentLineWidth = estimatedWordWidth;
      } else {
        currentLineWidth += estimatedWordWidth + wordSpacing;
      }
    });

    const contentHeight = totalLines * (lineHeight + lineSpacing);

    return contentHeight + 400; // 基础高度 + 内容高度 + 缓冲
  },

  // 渲染带标记的文本 - 简化版本
  renderMarkedText(ctx, markedText, x, startY, maxWidth, vocabType) {
    let yPos = startY;
    const lineHeight = 30;
    const fontSize = 16;

    ctx.setFontSize(fontSize);
    ctx.setTextAlign('left');

    // 将HTML转换为纯文本，但保留标记信息
    const textSegments = this.parseTextSegments(markedText);

    let currentX = x;
    const wordSpacing = 6;

    textSegments.forEach(segment => {
      const { text, isMarked, vocabType: segmentVocabType, isMultiple, vocabTypes } = segment;

      // 测量文本宽度
      const textWidth = ctx.measureText(text).width;
      const segmentWidth = textWidth + (isMarked ? 12 : 4); // 标记词汇额外宽度

      // 检查是否需要换行
      if (currentX + segmentWidth > x + maxWidth && currentX > x) {
        yPos += lineHeight;
        currentX = x;
      }

      // 绘制背景（如果是标记词汇）
      if (isMarked) {
        const bgColor = this.getVocabColor(segmentVocabType || vocabType);
        ctx.setFillStyle(bgColor);
        const rectX = currentX - 2;
        const rectY = yPos - fontSize - 2;
        const rectWidth = segmentWidth;
        const rectHeight = fontSize + 6;

        ctx.fillRect(rectX, rectY, rectWidth, rectHeight);

        // 绘制边框
        ctx.setStrokeStyle(this.getVocabBorderColor(segmentVocabType || vocabType));
        ctx.setLineWidth(1);
        ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);

        // 绘制重合词汇标记（橙色右上角三角形）
        if (isMultiple && vocabTypes && vocabTypes.length > 1) {
          ctx.setFillStyle('#FF9800');
          ctx.beginPath();
          const triangleSize = 8;
          const triangleX = rectX + rectWidth - triangleSize;
          const triangleY = rectY;

          ctx.moveTo(triangleX, triangleY);
          ctx.lineTo(triangleX + triangleSize, triangleY);
          ctx.lineTo(triangleX + triangleSize, triangleY + triangleSize);
          ctx.closePath();
          ctx.fill();
        }
      }

      // 绘制文字
      ctx.setFillStyle('#333333');
      ctx.fillText(text, currentX + 2, yPos);

      currentX += segmentWidth + wordSpacing;
    });

    return yPos + 50;
  },

  // 解析文本段落（支持重合词汇）
  parseTextSegments(html) {
    const segments = [];

    // 简化正则表达式，只匹配span标签和内容
    const regex = /<span[^>]*class="marked-word[^"]*"[^>]*>(.*?)<\/span>/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(html)) !== null) {
      // 添加标记前的普通文本
      if (match.index > lastIndex) {
        const plainText = html.substring(lastIndex, match.index);
        const words = plainText.match(/\S+/g) || [];
        words.forEach(word => {
          segments.push({ text: word, isMarked: false });
        });
      }

      // 添加标记的文本
      const markedText = match[1]; // 标记的单词
      const fullSpan = match[0]; // 完整的span标签

      // 手动解析data-vocabs属性
      const dataVocabsMatch = fullSpan.match(/data-vocabs="([^"]*)"/);
      const dataVocabs = dataVocabsMatch ? dataVocabsMatch[1] : null;

      // 检查是否是重合词汇
      const isMultiple = fullSpan.includes('marked-multiple');

      let vocabTypes = [];
      let primaryVocabType = 'gaokao';

      if (dataVocabs) {
        // 重合词汇，从data-vocabs解析
        vocabTypes = dataVocabs.split(',');
        primaryVocabType = vocabTypes[0];
      } else {
        // 单一词库，从class解析
        const vocabTypeMatch = fullSpan.match(/marked-(gaokao|cet4|cet6)/);
        primaryVocabType = vocabTypeMatch ? vocabTypeMatch[1] : 'gaokao';
        vocabTypes = [primaryVocabType];
      }

      segments.push({
        text: markedText,
        isMarked: true,
        vocabType: primaryVocabType,
        vocabTypes: vocabTypes,
        isMultiple: isMultiple
      });

      lastIndex = regex.lastIndex;
    }

    // 添加剩余的普通文本
    if (lastIndex < html.length) {
      const remainingText = html.substring(lastIndex);
      const words = remainingText.match(/\S+/g) || [];
      words.forEach(word => {
        segments.push({ text: word, isMarked: false });
      });
    }

    return segments;
  },



  // 绘制圆角矩形
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
    ctx.fill();
  },

  // 绘制圆角边框
  strokeRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
    ctx.stroke();
  },

  // 解析带标记的HTML文本为单词数组
  parseMarkedTextToWords(html) {
    const words = [];
    // 更新正则表达式以匹配所有类型的标记词汇，包括重合词汇
    const regex = /<span class="marked-word marked-(gaokao|cet4|cet6)(?:\s+marked-multiple)?"[^>]*data-vocabs="([^"]*)"[^>]*>(.*?)<\/span>|<span class="marked-word marked-(gaokao|cet4|cet6)"[^>]*>(.*?)<\/span>/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(html)) !== null) {
      // 处理标记前的普通文本
      if (match.index > lastIndex) {
        const plainText = html.substring(lastIndex, match.index);
        const plainWords = this.extractWordsFromText(plainText);
        plainWords.forEach(word => {
          if (word.trim()) {
            words.push({ word: word.trim(), isMarked: false });
          }
        });
      }

      // 处理标记的文本
      if (match[1] && match[3]) {
        // 重合词汇的情况
        const vocabTypes = match[2].split(','); // 多个词库类型
        const markedWord = match[3].trim(); // 标记的单词
        if (markedWord) {
          words.push({ word: markedWord, isMarked: true, vocabType: vocabTypes[0], vocabTypes: vocabTypes });
        }
      } else if (match[4] && match[5]) {
        // 单一词库的情况
        const vocabType = match[4]; // 词库类型 (gaokao, cet4, cet6)
        const markedWord = match[5].trim(); // 标记的单词
        if (markedWord) {
          words.push({ word: markedWord, isMarked: true, vocabType: vocabType });
        }
      }

      lastIndex = regex.lastIndex;
    }

    // 处理剩余的普通文本
    if (lastIndex < html.length) {
      const remainingText = html.substring(lastIndex);
      const remainingWords = this.extractWordsFromText(remainingText);
      remainingWords.forEach(word => {
        if (word.trim()) {
          words.push({ word: word.trim(), isMarked: false });
        }
      });
    }

    return words;
  },

  // 从文本中提取单词
  extractWordsFromText(text) {
    // 使用正则表达式提取英文单词，保留标点符号
    return text.match(/\b[a-zA-Z]+\b|[^\w\s]/g) || [];
  },

  // 解析带标记的HTML文本（保留原方法以备用）
  parseMarkedText(html) {
    const segments = [];
    const regex = /<span class="marked-word[^"]*"[^>]*>(.*?)<\/span>/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(html)) !== null) {
      // 添加标记前的普通文本
      if (match.index > lastIndex) {
        const plainText = html.substring(lastIndex, match.index);
        if (plainText.trim()) {
          segments.push({ text: plainText, isMarked: false });
        }
      }

      // 添加标记的文本
      segments.push({ text: match[1], isMarked: true });
      lastIndex = regex.lastIndex;
    }

    // 添加剩余的普通文本
    if (lastIndex < html.length) {
      const remainingText = html.substring(lastIndex);
      if (remainingText.trim()) {
        segments.push({ text: remainingText, isMarked: false });
      }
    }

    return segments;
  },

  // 获取词汇类型对应的背景色（与预览界面保持一致）
  getVocabColor(vocabType) {
    const colors = {
      'gaokao': '#FFCDD2',  // 浅红色背景，与预览界面一致
      'cet4': '#BBDEFB',    // 浅蓝色背景，与预览界面一致
      'cet6': '#C8E6C9'     // 浅绿色背景，与预览界面一致
    };
    return colors[vocabType] || '#FFCDD2';
  },

  // 获取词汇类型对应的边框色（与预览界面保持一致）
  getVocabBorderColor(vocabType) {
    const colors = {
      'gaokao': '#F44336',  // 红色边框，与预览界面一致
      'cet4': '#2196F3',    // 蓝色边框，与预览界面一致
      'cet6': '#4CAF50'     // 绿色边框，与预览界面一致
    };
    return colors[vocabType] || '#F44336';
  },

  // 文本换行处理
  wrapText(ctx, text, maxWidth, fontSize) {
    ctx.setFontSize(fontSize);
    const words = text.split(' ');
    const lines = [];
    let currentLine = '';

    for (let word of words) {
      const testLine = currentLine + (currentLine ? ' ' : '') + word;
      const metrics = ctx.measureText(testLine);

      if (metrics.width > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine = testLine;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines.slice(0, 30); // 限制行数
  },

  onShareAppMessage() {
    const { stats, selectedVocabType, vocabTypes } = this.data;
    const vocabName = vocabTypes.find(v => v.id === selectedVocabType)?.name || '';

    return {
      title: `文章词汇分析：${vocabName}词汇覆盖率${stats.coverage}%`,
      path: '/pages/teacher-tools/article-analysis/article-analysis',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 快速分析（点击词库按钮直接分析）
  async quickAnalysis(e) {
    const vocabType = e.currentTarget.dataset.type;
    console.log('开始快速分析，词库类型:', vocabType);
    
    if (!this.data.articleText || !this.data.articleText.trim()) {
      wx.showToast({
        title: '请先输入文章内容',
        icon: 'none'
      });
      return;
    }

    // 设置选中的词库类型
    this.setData({
      selectedVocabType: vocabType,
      loading: true
    });

    try {
      console.log('开始加载词库数据...');
      await this.loadVocabData();
      
      console.log('词库加载完成，开始分析文章...');
      const analyzedData = this.analyzeArticle(this.data.articleText);
      
      this.setData({
        analyzedArticle: analyzedData,
        currentStep: 1,
        loading: false
      });
      
      this.calculateStats();
      
      wx.showToast({
        title: '分析完成',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('分析文章失败:', error);
      wx.showModal({
        title: '分析失败',
        content: `错误信息: ${error.message}\n\n可能原因:\n1. 网络连接问题\n2. 词库数据未找到\n3. 云数据库连接失败`,
        showCancel: false,
        confirmText: '重试'
      });
      this.setData({ loading: false });
    }
  },

  // 快速分析（单词库模式）
  async quickAnalysis(e) {
    const vocabType = e.currentTarget.dataset.type;

    if (!this.data.articleText.trim()) {
      wx.showToast({
        title: '请先输入文章内容',
        icon: 'none'
      });
      return;
    }

    // 设置为单词库模式
    this.setData({
      selectedVocabTypes: [vocabType],
      loading: true
    });

    try {
      await this.loadVocabData();
      const analyzedData = this.analyzeArticle(this.data.articleText);

      this.setData({
        analyzedArticle: analyzedData,
        currentStep: 1,
        loading: false
      });

      this.calculateStats();

    } catch (error) {
      console.error('快速分析失败:', error);
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 切换词库并重新分析（兼容旧版本，现在使用toggleVocabType）
  async switchVocabAndReanalyze(e) {
    // 重定向到新的多选方法
    this.toggleVocabType(e);
  },

  // 获取当前词库颜色（多选模式下返回混合色）
  getCurrentVocabColor() {
    const { selectedVocabTypes, vocabTypes } = this.data;
    if (selectedVocabTypes.length === 1) {
      const vocab = vocabTypes.find(v => v.id === selectedVocabTypes[0]);
      return vocab ? vocab.color : '#ff6b6b';
    }
    return '#667eea'; // 多选时使用默认色
  },

  // 获取当前词库名称（多选模式下返回组合名称）
  getCurrentVocabName() {
    const { selectedVocabTypes, vocabTypes } = this.data;
    if (selectedVocabTypes.length === 1) {
      const vocab = vocabTypes.find(v => v.id === selectedVocabTypes[0]);
      return vocab ? vocab.name : '高考';
    }
    const names = selectedVocabTypes.map(type => {
      const vocab = vocabTypes.find(v => v.id === type);
      return vocab ? vocab.name : type;
    });
    return names.join('+');
  },




});
