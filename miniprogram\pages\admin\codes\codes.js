const randomString = length => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

Page({
  data: {
    count: 10,
    typeOptions: ['month', 'year', 'permanent'],
    typeIndex: 0,
    duration: 30,
    codes: []
  },
  onCountInput(e) {
    this.setData({ count: Number(e.detail.value) })
  },
  onTypeChange(e) {
    this.setData({ typeIndex: Number(e.detail.value) })
  },
  onDurationInput(e) {
    this.setData({ duration: Number(e.detail.value) })
  },
  async onGenerate() {
    const { count, typeOptions, typeIndex, duration } = this.data
    if (!count || count < 1) {
      wx.showToast({ title: '数量需大于0', icon: 'none' })
      return
    }
    wx.showLoading({ title: '生成中...' })
    const codes = []
    const db = wx.cloud.database()
    for (let i = 0; i < count; i++) {
      const code = randomString(8)
      codes.push(code)
      await db.collection('codes').add({
        data: {
          code,
          type: typeOptions[typeIndex],
          duration,
          used: false,
          usedBy: '',
          usedAt: null
        }
      })
    }
    wx.hideLoading()
    wx.showToast({ title: '生成成功', icon: 'success' })
    this.setData({ codes })
  }
}) 