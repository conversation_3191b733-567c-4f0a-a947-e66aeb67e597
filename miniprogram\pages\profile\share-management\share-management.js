Page({

  /**
   * 页面的初始数据
   */
  data: {
    shareId: '',
    shareData: null,
    loading: true,
    participants: [], // 参与者列表
    statistics: {
      totalParticipants: 0,
      totalTests: 0,
      averageScore: 0,
      highestScore: 0,
      completionRate: 0
    },
    showDetail: false,
    selectedParticipant: null,
    isMultiLevel: false,
    totalLevels: 1,
    currentTab: 'overview', // overview, participants, levels
    selectedMistakes: [], // 选中的错词
    allMistakesSelected: false, // 是否全选
    levelsData: [], // 关卡数据
    selectedMistakesMap: {}, // 选中错词的映射
    // 多组错词选择相关
    selectedGroupMistakes: [], // 选中的多组错词
    allGroupMistakesSelected: false, // 是否全选多组错词
    selectedGroupMistakesMap: {} // 选中多组错词的映射
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { shareId } = options;
    if (shareId) {
      this.setData({ shareId });
      this.loadShareData();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载数据
    if (this.data.shareId) {
      this.loadShareData();
    }
  },

  /**
   * 加载分享数据
   */
  async loadShareData() {
    try {
      this.setData({ loading: true });
      
      // 优先从云端获取分享数据
      let shareData = null;
      
      try {
        const result = await wx.cloud.callFunction({
          name: 'getShareTest',
          data: { shareId: this.data.shareId }
        });
        
        if (result.result.success) {
          shareData = result.result.data;
        }
      } catch (cloudError) {
        console.log('从云端获取分享数据失败:', cloudError);
      }
      
      // 如果云端没有数据，尝试从本地获取
      if (!shareData) {
        const shareTests = wx.getStorageSync('shareTests') || {};
        shareData = shareTests[this.data.shareId];
      }
      
      if (!shareData) {
        wx.showToast({
          title: '分享不存在',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 处理参与者数据
      const participants = this.processParticipants(shareData);
      
      // 计算统计信息
      const statistics = this.calculateStatistics(shareData, participants);
      
      // 如果是多关卡，计算关卡数据
      let levelsData = [];
      if (shareData.isMultiLevel) {
        levelsData = this.calculateLevelsData(shareData.totalLevels || 1, participants);
      }
      
      this.setData({
        shareData: shareData,
        participants: participants,
        statistics: statistics,
        isMultiLevel: shareData.isMultiLevel || false,
        totalLevels: shareData.totalLevels || 1,
        levelsData: levelsData,
        loading: false
      });
      
    } catch (error) {
      console.error('加载分享数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 处理参与者数据
   */
  processParticipants(shareData) {
    const participants = [];
    const visitors = shareData.visitors || [];
    const results = shareData.results || [];
    const levelProgress = shareData.levelProgress || {};
    
    visitors.forEach(visitor => {
      // 获取该用户的所有测试结果
      const userResults = results.filter(r => r.participantOpenid === visitor.openid);
      
      // 获取该用户的关卡进度
      const userProgress = levelProgress[visitor.openid];
      
      // 计算统计信息
      const testCount = userResults.length;
      const scores = userResults.map(r => r.score);
      const bestScore = scores.length > 0 ? Math.max(...scores) : 0;
      const averageScore = scores.length > 0 ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;
      const latestResult = userResults.length > 0 ? userResults[userResults.length - 1] : null;
      
      // 计算多组任务进度
      let groupProgress = null;
      if (shareData.isMultiLevel || shareData.totalGroups > 1) {
        const totalGroups = shareData.totalGroups || shareData.totalLevels || 1;
        const completedGroups = userProgress ? (userProgress.completedLevels || []).length : 0;
        const currentGroup = userProgress ? userProgress.currentLevel : 1;
        const progressPercentage = Math.round((completedGroups / totalGroups) * 100);

        // 收集所有错词
        const allMistakes = [];
        userResults.forEach(result => {
          if (result.mistakes && result.mistakes.length > 0) {
            allMistakes.push(...result.mistakes);
          }
        });

        // 去重错词并格式化
        const uniqueMistakes = [];
        const seenWords = new Set();
        allMistakes.forEach(mistake => {
          // 确保错词数据格式正确
          let wordText = '';
          let correctAnswer = '';
          let userAnswer = '';

          if (typeof mistake === 'string') {
            wordText = mistake;
          } else if (mistake && typeof mistake === 'object') {
            // 处理嵌套的word对象
            if (typeof mistake.word === 'object' && mistake.word !== null) {
              wordText = mistake.word.word || mistake.word.words || mistake.word.english || mistake.word.text || '';
            } else {
              wordText = mistake.word || mistake.text || mistake.english || mistake.question || '';
            }

            // 处理正确答案
            if (typeof mistake.correctAnswer === 'object' && mistake.correctAnswer !== null) {
              correctAnswer = mistake.correctAnswer.meaning || mistake.correctAnswer.chinese || mistake.correctAnswer.word || '';
            } else {
              correctAnswer = mistake.correctAnswer || mistake.chinese || mistake.meaning || '';
            }

            // 处理用户答案 - 添加更多字段支持
            if (typeof mistake.userAnswer === 'object' && mistake.userAnswer !== null) {
              userAnswer = mistake.userAnswer.meaning || mistake.userAnswer.word || mistake.userAnswer.text || '';
            } else if (typeof mistake.selectedAnswer === 'object' && mistake.selectedAnswer !== null) {
              userAnswer = mistake.selectedAnswer.meaning || mistake.selectedAnswer.word || mistake.selectedAnswer.text || '';
            } else {
              userAnswer = mistake.userAnswer || mistake.selectedAnswer || mistake.answer || '';
            }
          }

          // 确保wordText不为空且不是"[object Object]"
          if (wordText && wordText !== '[object Object]' && !seenWords.has(wordText)) {
            seenWords.add(wordText);
            uniqueMistakes.push({
              word: wordText,
              correctAnswer: correctAnswer || '未知',
              userAnswer: userAnswer || '未答'
            });
          }
        });

        groupProgress = {
          totalGroups: totalGroups,
          completedGroups: completedGroups,
          currentGroup: currentGroup,
          progressPercentage: progressPercentage,
          allMistakes: uniqueMistakes,
          isCompleted: completedGroups >= totalGroups
        };
      }

      // 尝试从测试结果中获取用户信息
      let userNickName = visitor.nickName || '匿名用户';
      let userAvatar = visitor.avatar || '';

      // 如果访问者信息中没有昵称，尝试从测试结果中获取
      if (userNickName === '匿名用户' && userResults.length > 0) {
        const latestResult = userResults[userResults.length - 1];
        if (latestResult.participantInfo) {
          userNickName = latestResult.participantInfo.nickName || userNickName;
          userAvatar = latestResult.participantInfo.avatar || userAvatar;
        }
      }

      participants.push({
        openid: visitor.openid,
        nickName: userNickName,
        avatar: userAvatar,
        firstVisitTime: visitor.firstVisitTime,
        lastTestTime: visitor.lastTestTime,
        visitCount: visitor.visitCount || 0,
        testCount: testCount,
        bestScore: bestScore,
        averageScore: averageScore,
        latestScore: latestResult ? latestResult.score : 0,
        latestAccuracy: latestResult ? latestResult.accuracy : 0,
        latestTestTime: latestResult ? latestResult.submitTime : null,
        mistakes: latestResult ? this.formatMistakes(latestResult.mistakes || []) : [],
        results: userResults,
        progress: userProgress,
        // 多关卡相关
        currentLevel: userProgress ? userProgress.currentLevel : 1,
        completedLevels: userProgress ? userProgress.completedLevels : [],
        levelScores: userProgress ? userProgress.scores : {},
        // 多组任务进度
        groupProgress: groupProgress
      });
    });
    
    // 按最近测试时间排序
    participants.sort((a, b) => {
      const timeA = a.latestTestTime || a.lastTestTime || 0;
      const timeB = b.latestTestTime || b.lastTestTime || 0;
      return timeB - timeA;
    });
    
    return participants;
  },

  /**
   * 格式化错词数据
   */
  formatMistakes(mistakes) {
    if (!mistakes || !Array.isArray(mistakes)) {
      return [];
    }

    return mistakes.map(mistake => {
      if (typeof mistake === 'string') {
        return {
          word: mistake,
          correctAnswer: '',
          userAnswer: ''
        };
      } else if (mistake && typeof mistake === 'object') {
        // 处理嵌套的word对象
        let wordText = '';
        if (typeof mistake.word === 'object' && mistake.word !== null) {
          wordText = mistake.word.word || mistake.word.words || mistake.word.english || mistake.word.text || '';
        } else {
          wordText = mistake.word || mistake.text || mistake.english || mistake.question || '';
        }

        // 处理正确答案
        let correctAnswer = '';
        if (typeof mistake.correctAnswer === 'object' && mistake.correctAnswer !== null) {
          correctAnswer = mistake.correctAnswer.meaning || mistake.correctAnswer.chinese || mistake.correctAnswer.word || '';
        } else {
          correctAnswer = mistake.correctAnswer || mistake.chinese || mistake.meaning || '';
        }

        // 处理用户答案
        let userAnswer = '';
        if (typeof mistake.userAnswer === 'object' && mistake.userAnswer !== null) {
          userAnswer = mistake.userAnswer.meaning || mistake.userAnswer.word || mistake.userAnswer.text || '';
        } else if (typeof mistake.selectedAnswer === 'object' && mistake.selectedAnswer !== null) {
          userAnswer = mistake.selectedAnswer.meaning || mistake.selectedAnswer.word || mistake.selectedAnswer.text || '';
        } else {
          userAnswer = mistake.userAnswer || mistake.selectedAnswer || mistake.answer || '';
        }

        return {
          word: wordText,
          correctAnswer: correctAnswer || '未知',
          userAnswer: userAnswer || '未答'
        };
      }
      return {
        word: '',
        correctAnswer: '',
        userAnswer: ''
      };
    }).filter(mistake => mistake.word && mistake.word !== '[object Object]'); // 过滤掉空的错词和对象字符串
  },

  /**
   * 计算统计信息
   */
  calculateStatistics(shareData, participants) {
    const results = shareData.results || [];
    const totalParticipants = participants.length;
    const totalTests = results.length;
    
    let totalScore = 0;
    let highestScore = 0;
    let completedParticipants = 0;
    
    results.forEach(result => {
      totalScore += result.score;
      highestScore = Math.max(highestScore, result.score);
    });
    
    // 计算完成率
    if (shareData.isMultiLevel) {
      participants.forEach(participant => {
        if (participant.completedLevels.length === shareData.totalLevels) {
          completedParticipants++;
        }
      });
    } else {
      completedParticipants = participants.filter(p => p.testCount > 0).length;
    }
    
    return {
      totalParticipants: totalParticipants,
      totalTests: totalTests,
      averageScore: totalTests > 0 ? Math.round(totalScore / totalTests) : 0,
      highestScore: highestScore,
      completionRate: totalParticipants > 0 ? Math.round((completedParticipants / totalParticipants) * 100) : 0
    };
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });
  },

  /**
   * 查看参与者详情
   */
  viewParticipantDetail(e) {
    const index = e.currentTarget.dataset.index;
    const participant = this.data.participants[index];
    
    this.setData({
      selectedParticipant: participant,
      showDetail: true,
      selectedMistakes: [], // 重置选中的错词
      selectedMistakesMap: {}, // 重置选中错词映射
      allMistakesSelected: false // 重置全选状态
    });
  },

  /**
   * 关闭详情弹窗
   */
  closeDetail() {
    this.setData({
      showDetail: false,
      selectedParticipant: null
    });
  },

  /**
   * 阻止冒泡
   */
  stopPropagation(e) {
    // 阻止事件冒泡
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    } else if (diff < 2592000000) {
      return `${Math.floor(diff / 86400000)}天前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 导出数据
   */
  exportData() {
    const { shareData, participants, statistics } = this.data;
    
    // 构建导出数据
    const exportData = {
      shareInfo: {
        shareId: shareData.shareId,
        testType: shareData.testType,
        libraryName: shareData.libraryName,
        createTime: shareData.createTime,
        expireTime: shareData.expireTime,
        isMultiLevel: shareData.isMultiLevel,
        totalLevels: shareData.totalLevels,
        wordsCount: shareData.words ? shareData.words.length : 0
      },
      statistics: statistics,
      participants: participants.map(p => ({
        nickName: p.nickName,
        testCount: p.testCount,
        bestScore: p.bestScore,
        averageScore: p.averageScore,
        latestScore: p.latestScore,
        latestAccuracy: p.latestAccuracy,
        currentLevel: p.currentLevel,
        completedLevels: p.completedLevels,
        firstVisitTime: this.formatTime(p.firstVisitTime),
        lastTestTime: this.formatTime(p.latestTestTime)
      }))
    };
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: JSON.stringify(exportData, null, 2),
      success: () => {
        wx.showToast({
          title: '数据已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 删除分享
   */
  deleteShare() {
    wx.showModal({
      title: '确认删除',
      content: '删除后所有参与者将无法继续访问此分享测试，是否确认删除？',
      confirmText: '确认删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteShare();
        }
      }
    });
  },

  /**
   * 执行删除分享
   */
  async doDeleteShare() {
    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });
      
      // 调用云函数删除分享
      const result = await wx.cloud.callFunction({
        name: 'deleteShareTest',
        data: {
          shareId: this.data.shareId,
          deleteType: 'creator' // 创建者删除
        }
      });
      
      wx.hideLoading();
      
      if (result.result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: result.result.message || '删除失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('删除分享失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  },

  /**
   * 分享给其他人
   */
  shareToOthers() {
    const { shareId, shareData } = this.data;
    
    wx.showActionSheet({
      itemList: ['复制测试ID', '分享给微信好友', '分享到企业微信'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.copyTestId();
            break;
          case 1:
            this.shareToWeChat();
            break;
          case 2:
            this.shareToEnterpriseWeChat();
            break;
        }
      }
    });
  },

  /**
   * 复制测试ID
   */
  copyTestId() {
    wx.setClipboardData({
      data: this.data.shareId,
      success: () => {
        wx.showToast({
          title: '测试ID已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 分享到微信
   */
  shareToWeChat() {
    const { shareData } = this.data;
    const testModeText = shareData.testType === 'en_to_cn' ? '英译汉' : '汉译英';
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage'],
      success: () => {
        wx.shareAppMessage({
          title: `${shareData.libraryName} - ${testModeText}测试`,
          path: `/pages/wordtest/test/test?shareId=${shareData.shareId}&shareMode=share&testMode=${shareData.testType}`,
          imageUrl: '/images/share-cover.png'
        });
      }
    });
  },

  /**
   * 分享到企业微信
   */
  shareToEnterpriseWeChat() {
    wx.navigateToMiniProgram({
      appId: 'wx2b03c6e691cd7370',
      path: '',
      extraData: {
        shareId: this.data.shareId,
        shareData: this.data.shareData
      },
      success: () => {
        console.log('跳转企业微信成功');
      },
      fail: (error) => {
        console.error('跳转企业微信失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadShareData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 切换错词选择
   */
  toggleMistakeSelection(e) {
    const word = e.currentTarget.dataset.word;
    const selectedMistakes = this.data.selectedMistakes;
    const selectedMistakesMap = this.data.selectedMistakesMap;
    const index = selectedMistakes.indexOf(word);
    
    if (index > -1) {
      selectedMistakes.splice(index, 1);
      delete selectedMistakesMap[word];
    } else {
      selectedMistakes.push(word);
      selectedMistakesMap[word] = true;
    }
    
    this.setData({
      selectedMistakes: selectedMistakes,
      selectedMistakesMap: selectedMistakesMap,
      allMistakesSelected: selectedMistakes.length === this.data.selectedParticipant.mistakes.length
    });
  },

  /**
   * 全选/取消全选错词
   */
  selectAllMistakes() {
    const allMistakesSelected = !this.data.allMistakesSelected;
    const mistakes = this.data.selectedParticipant.mistakes || [];
    
    let selectedMistakes = [];
    let selectedMistakesMap = {};
    
    if (allMistakesSelected) {
      selectedMistakes = mistakes.map(m => m.word);
      mistakes.forEach(m => {
        selectedMistakesMap[m.word] = true;
      });
    }
    
    this.setData({
      allMistakesSelected: allMistakesSelected,
      selectedMistakes: selectedMistakes,
      selectedMistakesMap: selectedMistakesMap
    });
  },

  /**
   * 显示创建测试选项
   */
  showCreateTestOptions() {
    const selectedMistakes = this.data.selectedMistakes;
    const mistakes = this.data.selectedParticipant.mistakes;
    
    // 获取选中的错词详细信息
    const selectedWords = mistakes.filter(m => selectedMistakes.includes(m.word));
    
    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择错词',
        icon: 'none'
      });
      return;
    }
    
    wx.showActionSheet({
      itemList: ['英译汉测试', '汉译英测试', '消消乐游戏', '听写测试'],
      success: (res) => {
        let testType = '';
        switch (res.tapIndex) {
          case 0:
            testType = 'en_to_cn';
            break;
          case 1:
            testType = 'cn_to_en';
            break;
          case 2:
            testType = 'elimination';
            break;
          case 3:
            testType = 'dictation';
            break;
        }
        
        if (testType) {
          this.createTestFromMistakes(testType, selectedWords);
        }
      }
    });
  },

  /**
   * 计算关卡数据
   */
  calculateLevelsData(totalLevels, participants) {
    const levelsData = [];

    for (let i = 0; i < totalLevels; i++) {
      const levelIndex = i + 1;

      // 计算完成这一关的参与者
      const completedParticipants = participants.filter(p =>
        p.completedLevels && p.completedLevels.includes(levelIndex)
      );

      // 计算有这一关分数的参与者
      const participantsWithScore = participants.filter(p =>
        p.levelScores && p.levelScores[levelIndex]
      );

      // 计算平均分
      let averageScore = 0;
      if (participantsWithScore.length > 0) {
        const totalScore = participantsWithScore.reduce((sum, p) =>
          sum + (p.levelScores[levelIndex] || 0), 0
        );
        averageScore = Math.round(totalScore / participantsWithScore.length);
      }

      // 计算完成率
      const completionRate = participants.length > 0 ?
        Math.round((completedParticipants.length / participants.length) * 100) : 0;

      // 准备这一关的所有参与者列表（包括未完成的）
      const levelParticipants = participants.map(p => {
        const isCompleted = p.completedLevels && p.completedLevels.includes(levelIndex);
        const score = p.levelScores ? (p.levelScores[levelIndex] || 0) : 0;
        const hasAttempted = p.levelScores && p.levelScores[levelIndex] !== undefined;

        return {
          openid: p.openid,
          nickName: p.nickName,
          avatar: p.avatar,
          score: score,
          isCompleted: isCompleted,
          hasAttempted: hasAttempted,
          status: isCompleted ? '已完成' : (hasAttempted ? '未完成' : '未开始')
        };
      }).sort((a, b) => {
        // 排序：已完成的在前，然后按分数排序
        if (a.isCompleted && !b.isCompleted) return -1;
        if (!a.isCompleted && b.isCompleted) return 1;
        if (a.isCompleted && b.isCompleted) return b.score - a.score;
        if (a.hasAttempted && !b.hasAttempted) return -1;
        if (!a.hasAttempted && b.hasAttempted) return 1;
        return b.score - a.score;
      });

      levelsData.push({
        levelIndex: levelIndex,
        completedCount: completedParticipants.length,
        completionRate: completionRate,
        averageScore: averageScore,
        participants: levelParticipants,
        totalParticipants: participants.length
      });
    }

    return levelsData;
  },

  /**
   * 从错词创建测试
   */
  createTestFromMistakes(testType, selectedWords) {
    // 准备词汇数据
    const wordsData = selectedWords.map(mistake => ({
      word: mistake.word,
      translation: mistake.translation || mistake.correct,
      phonetic: mistake.phonetic || '',
      example: mistake.example || '',
      exampleTranslation: mistake.exampleTranslation || ''
    }));
    
    // 保存到全局数据
    const app = getApp();
    app.globalData.selectedWordsForTest = wordsData;
    app.globalData.learningData = {
      words: wordsData,
      libraryId: 'mistake_review',
      libraryName: '错词复习'
    };
    
    // 根据测试类型跳转到对应页面
    let url = '';
    if (testType === 'en_to_cn' || testType === 'cn_to_en') {
      url = `/pages/wordtest/mode-select/mode-select?testMode=${testType}&libraryId=mistake_review`;
    } else if (testType === 'elimination') {
      url = `/pages/wordtest/mode-select/mode-select?testMode=elimination&libraryId=mistake_review`;
    } else if (testType === 'dictation') {
      url = `/pages/spelling/mode-select/mode-select?libraryId=mistake_review`;
    }
    
    if (url) {
      wx.navigateTo({
        url: url,
        success: () => {
          // 关闭详情弹窗
          this.setData({
            showDetail: false,
            selectedMistakes: [],
            allMistakesSelected: false
          });
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });
    }
  },

  /**
   * 全选/取消全选多组错词
   */
  selectAllGroupMistakes() {
    const allGroupMistakesSelected = !this.data.allGroupMistakesSelected;
    const mistakes = this.data.selectedParticipant.groupProgress?.allMistakes || [];

    let selectedGroupMistakes = [];
    let selectedGroupMistakesMap = {};

    if (allGroupMistakesSelected) {
      selectedGroupMistakes = mistakes.map(m => m.word);
      mistakes.forEach(m => {
        selectedGroupMistakesMap[m.word] = true;
      });
    }

    this.setData({
      selectedGroupMistakes,
      selectedGroupMistakesMap,
      allGroupMistakesSelected
    });
  },

  /**
   * 切换多组错词选择
   */
  toggleGroupMistakeSelection(e) {
    const word = e.currentTarget.dataset.word;
    const selectedGroupMistakes = this.data.selectedGroupMistakes;
    const selectedGroupMistakesMap = this.data.selectedGroupMistakesMap;
    const index = selectedGroupMistakes.indexOf(word);

    if (index > -1) {
      selectedGroupMistakes.splice(index, 1);
      selectedGroupMistakesMap[word] = false;
    } else {
      selectedGroupMistakes.push(word);
      selectedGroupMistakesMap[word] = true;
    }

    this.setData({
      selectedGroupMistakes,
      selectedGroupMistakesMap,
      allGroupMistakesSelected: false
    });
  },

  /**
   * 显示多组错词测试选项
   */
  showCreateGroupTestOptions() {
    const selectedGroupMistakes = this.data.selectedGroupMistakes;
    const mistakes = this.data.selectedParticipant.groupProgress?.allMistakes || [];

    // 获取选中的错词详细信息
    const selectedWords = mistakes.filter(m => selectedGroupMistakes.includes(m.word));

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择错词',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['英译汉测试', '汉译英测试', '消消乐游戏', '听写测试'],
      success: (res) => {
        let testType = '';
        switch (res.tapIndex) {
          case 0:
            testType = 'en_to_cn';
            break;
          case 1:
            testType = 'cn_to_en';
            break;
          case 2:
            testType = 'elimination';
            break;
          case 3:
            testType = 'dictation';
            break;
        }

        if (testType) {
          this.createTestFromGroupMistakes(testType, selectedWords);
        }
      }
    });
  },

  /**
   * 从多组错词创建测试
   */
  createTestFromGroupMistakes(testType, selectedWords) {
    // 准备词汇数据
    const wordsData = selectedWords.map(mistake => ({
      word: mistake.word,
      translation: mistake.translation || mistake.correct,
      phonetic: mistake.phonetic || '',
      example: mistake.example || '',
      exampleTranslation: mistake.exampleTranslation || ''
    }));

    // 保存到全局数据
    const app = getApp();
    app.globalData.selectedWordsForTest = wordsData;
    app.globalData.learningData = {
      words: wordsData,
      libraryId: 'group_mistake_review',
      libraryName: '多组错词复习'
    };

    // 根据测试类型跳转到对应页面
    let url = '';
    if (testType === 'en_to_cn' || testType === 'cn_to_en') {
      url = `/pages/wordtest/mode-select/mode-select?testMode=${testType}&libraryId=group_mistake_review`;
    } else if (testType === 'elimination') {
      url = `/pages/wordtest/mode-select/mode-select?testMode=elimination&libraryId=group_mistake_review`;
    } else if (testType === 'dictation') {
      url = `/pages/spelling/mode-select/mode-select?libraryId=group_mistake_review`;
    }

    if (url) {
      wx.navigateTo({
        url: url,
        success: () => {
          // 关闭详情弹窗
          this.setData({
            showDetail: false,
            selectedGroupMistakes: [],
            selectedGroupMistakesMap: {},
            allGroupMistakesSelected: false
          });
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });
    }
  }
});