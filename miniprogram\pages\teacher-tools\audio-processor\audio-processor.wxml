<view class="container">
  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step {{currentStep === 'upload' ? 'active' : currentStep === 'process' || currentStep === 'preview' || currentStep === 'complete' ? 'completed' : ''}}">
      <view class="step-number">1</view>
      <view class="step-label">上传文件</view>
    </view>
    <view class="step-line {{currentStep === 'process' || currentStep === 'preview' || currentStep === 'complete' ? 'completed' : ''}}"></view>
    <view class="step {{currentStep === 'process' ? 'active' : currentStep === 'preview' || currentStep === 'complete' ? 'completed' : ''}}">
      <view class="step-number">2</view>
      <view class="step-label">智能处理</view>
    </view>
    <view class="step-line {{currentStep === 'preview' || currentStep === 'complete' ? 'completed' : ''}}"></view>
    <view class="step {{currentStep === 'preview' ? 'active' : currentStep === 'complete' ? 'completed' : ''}}">
      <view class="step-number">3</view>
      <view class="step-label">预览确认</view>
    </view>
    <view class="step-line {{currentStep === 'complete' ? 'completed' : ''}}"></view>
    <view class="step {{currentStep === 'complete' ? 'active' : ''}}">
      <view class="step-number">4</view>
      <view class="step-label">完成</view>
    </view>
  </view>

  <!-- 上传文件步骤 -->
  <view class="step-content" wx:if="{{currentStep === 'upload'}}">
    <view class="upload-section">
      <view class="section-title">📁 文件上传</view>
      
      <!-- 音频文件上传 -->
      <view class="upload-card">
        <view class="upload-header">
          <view class="upload-icon">🎵</view>
          <view class="upload-info">
            <view class="upload-title">音频文件</view>
            <view class="upload-desc">支持MP3、WAV、M4A格式，最大50MB</view>
          </view>
        </view>
        
        <view class="upload-content">
          <view class="file-info" wx:if="{{audioFile}}">
            <view class="file-name">{{audioFile.name}}</view>
            <view class="file-size">{{audioFile.sizeText}}</view>
          </view>
          <button class="upload-btn" bindtap="chooseAudioFile">
            {{audioFile ? '重新选择' : '选择音频文件'}}
          </button>
        </view>
      </view>
      
      <!-- Word文档上传 -->
      <view class="upload-card">
        <view class="upload-header">
          <view class="upload-icon">📄</view>
          <view class="upload-info">
            <view class="upload-title">题目文档</view>
            <view class="upload-desc">Word格式(.doc/.docx)，包含完整题目内容</view>
          </view>
        </view>

        <view class="upload-content">
          <view class="file-info" wx:if="{{wordFile}}">
            <view class="file-name">{{wordFile.name}}</view>
            <view class="file-size">{{wordFile.sizeText}}</view>
          </view>
          <button class="upload-btn" bindtap="chooseWordFile">
            {{wordFile ? '重新选择' : '选择Word文档'}}
          </button>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="upload-card info-card">
        <view class="upload-header">
          <view class="upload-icon">💡</view>
          <view class="upload-info">
            <view class="upload-title">使用说明</view>
            <view class="upload-desc">上传音频文件和Word文档后，系统将智能分割音频并匹配题目</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 题型选择 -->
    <view class="config-section">
      <view class="section-title">⚙️ 处理配置</view>
      
      <view class="config-card">
        <view class="config-item">
          <view class="config-label">题型选择</view>
          <picker
            range="{{questionTypes}}"
            range-key="name"
            value="{{selectedQuestionTypeIndex}}"
            bindchange="onQuestionTypeChange"
          >
            <view class="config-picker">
              <text>{{selectedQuestionTypeName}}</text>
              <text class="picker-arrow">›</text>
            </view>
          </picker>
        </view>
        
        <view class="type-description">
          {{selectedQuestionTypeDescription}}
        </view>
      </view>
      
      <!-- 高级参数 -->
      <view class="config-card">
        <view class="config-title">高级参数</view>
        
        <view class="config-item">
          <view class="config-label">静音阈值</view>
          <view class="config-value">{{silenceThresholdDisplay}}</view>
        </view>
        <view class="slider-container">
          <slider
            min="0.05"
            max="0.30"
            step="0.01"
            value="{{silenceThreshold}}"
            data-parameter="silenceThreshold"
            bindchange="onParameterChange"
            bindchanging="onParameterChanging"
            activeColor="#4F46E5"
            backgroundColor="#e5e7eb"
            block-size="20"
            show-value="false"
          />
        </view>
        
        <view class="config-item">
          <view class="config-label">最小片段长度(秒)</view>
          <view class="config-value">{{minSegmentLengthDisplay}}</view>
        </view>
        <view class="slider-container">
          <slider
            min="1"
            max="10"
            step="1"
            value="{{minSegmentLength}}"
            data-parameter="minSegmentLength"
            bindchange="onParameterChange"
            bindchanging="onParameterChanging"
            activeColor="#4F46E5"
            backgroundColor="#e5e7eb"
            block-size="20"
            show-value="false"
          />
        </view>
        
        <view class="config-item">
          <view class="config-label">最大片段长度(秒)</view>
          <view class="config-value">{{maxSegmentLengthDisplay}}</view>
        </view>
        <view class="slider-container">
          <slider
            min="30"
            max="120"
            step="10"
            value="{{maxSegmentLength}}"
            data-parameter="maxSegmentLength"
            bindchange="onParameterChange"
            bindchanging="onParameterChanging"
            activeColor="#4F46E5"
            backgroundColor="#e5e7eb"
            block-size="20"
            show-value="false"
          />
        </view>
      </view>
    </view>

    <!-- 开始处理按钮 -->
    <view class="action-section">
      <button
        class="process-btn {{audioFile && wordFile ? 'enabled' : 'disabled'}}"
        bindtap="startProcessing"
        disabled="{{!audioFile || !wordFile || processing}}"
      >
        {{processing ? '处理中...' : '开始智能处理'}}
      </button>
    </view>
  </view>

  <!-- 处理进度步骤 -->
  <view class="step-content" wx:if="{{currentStep === 'process'}}">
    <view class="process-section">
      <view class="process-header">
        <view class="process-icon">⚡</view>
        <view class="process-title">智能处理中</view>
        <view class="process-desc">正在分析音频并提取题目信息...</view>
      </view>
      
      <view class="progress-container">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{processProgress}}%"></view>
        </view>
        <view class="progress-text">{{processProgress}}%</view>
      </view>
      
      <view class="process-status">{{processStatus}}</view>
      
      <view class="process-tips">
        <view class="tip-item">🎯 自动识别音频中的静音间隔</view>
        <view class="tip-item">✂️ 智能分割音频片段</view>
        <view class="tip-item">📝 解析Word文档中的题目结构</view>
        <view class="tip-item">🔗 建立音频与题目的对应关系</view>
      </view>
    </view>
  </view>

  <!-- 预览确认步骤 -->
  <view class="step-content" wx:if="{{currentStep === 'preview'}}">
    <view class="preview-section">
      <view class="preview-header">
        <view class="preview-title">处理结果预览</view>
        <view class="preview-stats">
          共分割出 <text class="highlight">{{audioSegments.length}}</text> 个音频片段，
          解析出 <text class="highlight">{{questionData.length}}</text> 道题目
        </view>
      </view>
      
      <!-- 音频片段列表 -->
      <view class="segments-list">
        <view class="segment-item" wx:for="{{audioSegments}}" wx:key="index">
          <view class="segment-info">
            <view class="segment-title">片段 {{index + 1}}</view>
            <view class="segment-details">
              <text class="segment-duration">时长: {{item.duration}}s</text>
              <text class="segment-size">大小: {{item.sizeText}}</text>
            </view>
          </view>
          
          <view class="segment-actions">
            <button 
              class="preview-btn {{currentPreviewIndex === index && isPlaying ? 'playing' : ''}}"
              data-index="{{index}}"
              bindtap="previewSegment"
            >
              {{currentPreviewIndex === index && isPlaying ? '播放中' : '试听'}}
            </button>
          </view>
        </view>
      </view>
      
      <!-- 题目信息预览 -->
      <view class="questions-preview" wx:if="{{questionData.length > 0}}">
        <view class="questions-title">题目信息</view>
        <view class="question-item" wx:for="{{questionData}}" wx:key="index">
          <view class="question-number">第{{index + 1}}题</view>
          <view class="question-content">{{item.content || item.question}}</view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="preview-actions">
        <button class="action-btn secondary" bindtap="restart">重新处理</button>
        <button class="action-btn primary" bindtap="saveToListening">保存到听口训练</button>
      </view>
    </view>
  </view>

  <!-- 完成步骤 -->
  <view class="step-content" wx:if="{{currentStep === 'complete'}}">
    <view class="complete-section">
      <view class="complete-icon">🎉</view>
      <view class="complete-title">处理完成！</view>
      <view class="complete-desc">
        音频已成功分割并保存到听口训练模块，
        学生现在可以进行分题练习了。
      </view>
      
      <view class="complete-actions">
        <button class="action-btn secondary" bindtap="restart">处理新文件</button>
        <navigator url="/pages/listening/listening" class="action-btn primary">查看听口训练</navigator>
      </view>
    </view>
  </view>
</view>
