// pages/wordtest/passage-select/passage-select.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    passageTypes: [
      {
        id: 'reading_3500',
        title: '40篇阅读搞定3500词',
        subtitle: '精选阅读材料，系统掌握核心词汇',
        icon: '📖',
        color: 'blue'
      },
      {
        id: 'sentence_3500',
        title: '100个句子搞定3500',
        subtitle: '经典句型练习，快速提升语感',
        icon: '📝',
        color: 'green'
      },
      {
        id: 'classic_novels',
        title: '英语名著与小说',
        subtitle: '经典文学作品，提升文化素养',
        icon: '📚',
        color: 'purple'
      },
      {
        id: 'foreign_media',
        title: '外刊阅读',
        subtitle: '精选外刊内容，了解时事热点',
        icon: '📰',
        color: 'orange'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '短文翻译'
    });
  },

  /**
   * 选择短文类型
   */
  onSelectPassageType(e) {
    const typeId = e.currentTarget.dataset.id;
    const selectedType = this.data.passageTypes.find(type => type.id === typeId);
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 根据不同类型跳转到对应页面
    switch(typeId) {
      case 'foreign_media':
        // 跳转到外刊阅读页面
        wx.navigateTo({
          url: '/pages/reading/foreign-media/foreign-media'
        });
        break;
      case 'classic_novels':
        // 跳转到英语名著小说页面
        wx.navigateTo({
          url: '/pages/reading/classic-novels/classic-novels'
        });
        break;
      default:
        // 其他功能显示开发中提示
        wx.showModal({
          title: selectedType.title,
          content: '该功能正在完善中，敬请期待！',
          showCancel: false,
          confirmText: '知道了',
          confirmColor: '#007aff'
        });
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  }
}); 