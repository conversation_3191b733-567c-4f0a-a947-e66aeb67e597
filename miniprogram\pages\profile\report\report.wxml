<!--pages/profile/report/report.wxml-->
<view class="container">
  <view class="main-content">
    <!-- 步骤一：基本信息填写 -->
  <view class="step-section {{currentStep === 0 ? 'active' : currentStep > 0 ? 'completed' : ''}}" wx:if="{{currentStep === 0}}">
    <view class="step-header">
      <view class="step-number">1</view>
      <text class="step-title">填写基本信息</text>
    </view>
    
    <view class="form-section">
      <view class="form-group">
        <text class="label">学员姓名</text>
        <input 
          class="input" 
          placeholder="请输入学员姓名" 
          value="{{basicInfo.studentName}}"
          bindinput="onStudentNameChange"
        />
      </view>
      
      <view class="form-group">
        <text class="label">年级</text>
        <input 
          class="input" 
          placeholder="高一 / 高二 / 高三" 
          value="{{basicInfo.grade}}"
          bindinput="onGradeChange"
        />
      </view>
      
      <view class="form-group">
        <text class="label">考试类型</text>
        <input 
          class="input" 
          placeholder="月考 / 期中 / 期末 / 一模 / 二模" 
          value="{{basicInfo.examType}}"
          bindinput="onExamTypeChange"
        />
      </view>
      
      <view class="form-group">
        <text class="label">分析人</text>
        <input 
          class="input" 
          placeholder="请输入分析人姓名" 
          value="{{basicInfo.analystName}}"
          bindinput="onAnalystNameChange"
        />
      </view>

    </view>
    
    <view class="step-buttons">
      <view class="btn primary" bindtap="nextStep">下一步</view>
    </view>
  </view>

  <!-- 步骤二：题型选择 -->
  <view class="step-section {{currentStep === 1 ? 'active' : currentStep > 1 ? 'completed' : ''}}" wx:if="{{currentStep === 1}}">
    <view class="step-header">
      <view class="step-number">2</view>
      <text class="step-title">选择题型</text>
    </view>
    
    <view class="instruction">
      <text class="instruction-text">请勾选这张试卷包含的题型：</text>
    </view>
    
    <view class="section-selection">
      <!-- 听口考试整体选择 -->
      <view class="section-group">
        <view class="section-group-header">
          <view 
            class="section-item group-item {{listeningExamSelected ? 'selected' : ''}}"
            bindtap="toggleListeningExam"
          >
            <view class="section-checkbox">
              <text class="checkbox-icon {{listeningExamSelected ? 'checked' : ''}}">{{listeningExamSelected ? '✓' : ''}}</text>
            </view>
            <view class="section-info">
              <text class="section-name group-name">听口考试</text>
              <text class="section-score">50分</text>
            </view>
          </view>
        </view>
        
        <!-- 听口小题型 -->
        <view class="section-sub-items">
          <view 
            class="section-item sub-item {{item.enabled ? 'selected' : ''}}"
            wx:for="{{sections}}"
            wx:for-index="index"
            wx:for-item="item"
            wx:key="id"
            wx:if="{{item.id.indexOf('listening_') === 0}}"
            data-index="{{index}}"
            bindtap="toggleSection"
          >
            <view class="section-checkbox">
              <text class="checkbox-icon {{item.enabled ? 'checked' : ''}}">{{item.enabled ? '✓' : ''}}</text>
            </view>
            <view class="section-info">
              <text class="section-name">{{item.name}}</text>
              <text class="section-score">{{item.totalScore}}分</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 其他题型 -->
      <view class="section-group">
        <view 
          class="section-item {{item.enabled ? 'selected' : ''}}"
          wx:for="{{sections}}"
          wx:for-index="index"
          wx:for-item="item"
          wx:key="id"
          wx:if="{{item.id.indexOf('listening_') !== 0}}"
          data-index="{{index}}"
          bindtap="toggleSection"
        >
          <view class="section-checkbox">
            <text class="checkbox-icon {{item.enabled ? 'checked' : ''}}">{{item.enabled ? '✓' : ''}}</text>
          </view>
          <view class="section-info">
            <text class="section-name">{{item.name}}</text>
            <text class="section-score">{{item.totalScore}}分</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="selection-summary">
      <text class="summary-text">已选择 {{enabledSectionsCount}} 个题型，总分 {{totalSelectedScore}} 分</text>
    </view>
    
    <view class="step-buttons">
      <view class="btn secondary" bindtap="prevStep">上一步</view>
      <view class="btn primary {{enabledSectionsCount === 0 ? 'disabled' : ''}}" bindtap="nextStep">下一步</view>
    </view>
  </view>

  <!-- 步骤三：题型分析填写 -->
  <view class="step-section {{currentStep === 2 ? 'active' : currentStep > 2 ? 'completed' : ''}}" wx:if="{{currentStep === 2}}">
    <view class="step-header">
      <view class="step-number">3</view>
      <text class="step-title">题型分析填写</text>
    </view>
    
    <!-- 当前题型进度提示 -->
    <view class="section-progress">
      <text class="progress-text">第 {{currentSectionIndex + 1}} 个题型 / 共 {{enabledSections.length}} 个题型</text>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{((currentSectionIndex + 1) / enabledSections.length) * 100}}%"></view>
      </view>
    </view>
    
    <!-- 当前题型分析 -->
    <view class="section-analysis" wx:if="{{currentSection}}">
      <view class="section-title">{{currentSection.name}}</view>
      

        <view class="score-input">
          <view class="score-group">
            <text class="score-label">总分</text>
            <input 
              class="score-field" 
              type="digit"
              decimal="{{true}}"
              maxlength="8"
              placeholder="总分" 
              value="{{currentSection.totalScore}}" 
              data-field="totalScore"
              bindinput="onScoreChange"
              confirm-type="done"
            />
          </view>
          <view class="score-group">
            <text class="score-label">失分</text>
            <input 
              class="score-field" 
              type="digit"
              decimal="{{true}}"
              maxlength="10"
              placeholder="请输入失分" 
              value="{{currentSection.lostScore === 0 ? '' : currentSection.lostScore}}"
              data-field="lostScore"
              bindinput="onScoreChange"
              bindfocus="onScoreFocus"
              confirm-type="done"
              adjust-position="{{false}}"
              cursor-spacing="100"
            />
          </view>
          <view class="score-group calculated">
            <text class="score-label">得分</text>
            <text class="score-value">{{currentSection.totalScore - (currentSection.lostScore || 0)}}</text>
          </view>
        </view>
        
        <!-- 原因分析 -->
        <view class="analysis-group">
          <text class="group-title">原因分析</text>
          <view class="options-grid">
            <view 
              class="option-item {{item.selected ? 'selected' : ''}}"
              wx:for="{{currentSection.reasons}}"
              wx:key="text"
              data-type="reasons"
              data-index="{{index}}"
              bindtap="toggleOption"
            >
              <text class="option-text">{{item.text}}</text>
            </view>
          </view>
          <view class="custom-input">
            <input 
              class="input" 
              placeholder="自定义原因..." 
              value="{{currentSection.customReason}}"
              data-field="customReason"
              bindinput="onCustomChange"
            />
          </view>
        </view>
        
        <!-- 学习建议 -->
        <view class="analysis-group">
          <text class="group-title">学习建议</text>
          <view class="options-grid">
            <view 
              class="option-item {{item.selected ? 'selected' : ''}}"
              wx:for="{{currentSection.suggestions}}"
              wx:key="text"
              data-type="suggestions"
              data-index="{{index}}"
              bindtap="toggleOption"
            >
              <text class="option-text">{{item.text}}</text>
            </view>
          </view>
          <view class="custom-input">
            <input 
              class="input" 
              placeholder="自定义建议..." 
              value="{{currentSection.customSuggestion}}"
              data-field="customSuggestion"
              bindinput="onCustomChange"
            />
          </view>
        </view>
      </view>
    
    <view class="step-buttons">
      <view class="btn secondary" bindtap="prevStep">{{currentSectionIndex === 0 ? '上一步' : '上一题型'}}</view>
      <view class="btn primary" bindtap="nextStep" style="background: linear-gradient(135deg, {{styles[selectedStyle].primary}}, {{styles[selectedStyle].secondary}});">{{currentSectionIndex === enabledSections.length - 1 ? '生成报告' : '下一题型'}}</view>
    </view>
  </view>

  <!-- 步骤三点五：课程规划选择 -->
  <view class="step-section {{currentStep === 3 ? 'active' : ''}}" wx:if="{{currentStep === 3}}">
    <view class="step-header">
      <view class="step-number">3.5</view>
      <text class="step-title">课程规划</text>
    </view>
    
    <view class="courseplan-selection">
              <view class="selection-info">
          <text class="info-title">课程规划设置</text>
          <text class="info-desc">您可以在下方直接编辑课程规划表格，填写完成后点击"使用课程规划"，或者跳过此步骤直接生成报告</text>
        </view>
      
      <view class="courseplan-preview">
        <view class="preview-title">课程规划编辑</view>
        <view class="preview-table">
          <!-- 表格头部 -->
          <view class="preview-header">
            <view class="preview-th" style="width: 120rpx;">
              <input 
                class="preview-header-input" 
                placeholder="课次" 
                value="{{coursePlanHeaders.lesson}}"
                data-field="lesson"
                bindinput="onCoursePlanHeaderChange"
              />
            </view>
            <view class="preview-th" style="flex: 1;">
              <input 
                class="preview-header-input" 
                placeholder="学习内容" 
                value="{{coursePlanHeaders.content}}"
                data-field="content"
                bindinput="onCoursePlanHeaderChange"
              />
            </view>
            <view class="preview-th" style="width: 80rpx;">操作</view>
          </view>
          
          <!-- 表格内容 -->
          <view class="preview-body">
            <view 
              class="preview-row {{index % 2 === 0 ? 'even' : 'odd'}}"
              wx:for="{{coursePlanData}}"
              wx:key="id"
            >
              <view class="preview-td" style="width: 120rpx;">
                <text class="lesson-text">第{{item.lesson}}讲</text>
              </view>
              <view class="preview-td" style="flex: 1;">
                <input 
                  class="preview-input" 
                  placeholder="请输入学习内容" 
                  value="{{item.content}}"
                  data-index="{{index}}"
                  bindinput="onCoursePlanContentChange"
                />
              </view>
              <view class="preview-td" style="width: 80rpx;">
                <button 
                  class="preview-btn delete-btn" 
                  data-index="{{index}}"
                  bindtap="removeCoursePlanRow"
                >删除</button>
              </view>
            </view>
          </view>
          
          <!-- 添加行按钮 -->
          <button class="add-plan-btn" bindtap="addCoursePlanRow">
            <text class="add-icon">+</text>
            <text>添加课程</text>
          </button>
        </view>
      </view>
      
      <view class="selection-buttons">
        <view class="btn secondary" bindtap="skipCoursePlan">
          <text class="btn-icon">⏭️</text>
          <text class="btn-text">跳过此步骤</text>
        </view>
        <view class="btn primary" bindtap="confirmCoursePlan">
          <text class="btn-icon">✅</text>
          <text class="btn-text">使用课程规划</text>
        </view>
      </view>
    </view>
    
    <view class="step-buttons">
      <view class="btn secondary" bindtap="prevStep">上一步</view>
    </view>
  </view>

  <!-- 步骤四：报告预览 -->
  <view class="step-section {{currentStep === 4 ? 'active' : ''}}" wx:if="{{currentStep === 4}}">
    <view class="step-header">
      <view class="step-number">4</view>
      <text class="step-title">试卷分析报告</text>
    </view>
    
    <view class="report-actions">
      <!-- 操作按钮 -->
      <view class="action-buttons-horizontal">
        <view class="btn-compact secondary" bindtap="prevStep">
          <text class="btn-icon">⬅️</text>
          <text class="btn-text">返回编辑</text>
        </view>
        <view class="btn-compact primary" bindtap="showShareOptions" data-type="analysis" style="background: linear-gradient(135deg, {{styles[selectedStyle].primary}}, {{styles[selectedStyle].secondary}});">
          <text class="btn-icon">📊</text>
          <text class="btn-text">导出分析报告</text>
        </view>
        <view class="btn-compact primary" wx:if="{{reportConfig.showCourseplan}}" bindtap="showShareOptions" data-type="courseplan" style="background: linear-gradient(135deg, {{styles[selectedStyle].primary}}, {{styles[selectedStyle].secondary}});">
          <text class="btn-icon">📅</text>
          <text class="btn-text">导出学习规划</text>
        </view>
        <view class="btn-compact primary" wx:if="{{reportConfig.showCourseplan}}" bindtap="showShareOptions" data-type="complete" style="background: linear-gradient(135deg, {{styles[selectedStyle].primary}}, {{styles[selectedStyle].secondary}});">
          <text class="btn-icon">📤</text>
          <text class="btn-text">导出完整内容</text>
        </view>
      </view>
      
      <!-- 紧凑样式选择 -->
      <view class="style-selector-compact">
        <text class="selector-label-small">样式：</text>
        <view class="style-options-compact">
          <view 
            class="style-dot {{selectedStyle === item.id ? 'active' : ''}}"
            wx:for="{{styles}}"
            wx:key="id"
            data-index="{{item.id}}"
            bindtap="selectStyle"
            style="background: linear-gradient(135deg, {{item.primary}}, {{item.secondary}});"
          ></view>
        </view>
      </view>
    </view>
    
    <!-- 隐藏的画布用于生成图片 -->
    <canvas
      canvas-id="reportCanvas"
      class="hidden-canvas"
      style="width: {{canvasWidth || 1125}}px; height: 6000px; position: fixed; top: -7000px; left: -4000px; z-index: -999;"
    ></canvas>
    
    <!-- 精美报告预览 -->
    <view class="report-preview" wx:if="{{reportData}}">
      <!-- 报告头部 -->
      <view class="report-header" style="background: linear-gradient(135deg, {{styles[selectedStyle].primary}} 0%, {{styles[selectedStyle].secondary}} 50%, {{styles[selectedStyle].accent}} 100%);">
        <view class="header-decoration">
          <view class="decoration-line"></view>
          <view class="decoration-icon">📊</view>
          <view class="decoration-line"></view>
        </view>
        <view class="report-title" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 100%;">{{reportData.basicInfo.studentName}}同学{{reportData.basicInfo.grade}}英语{{reportData.basicInfo.examType}}试卷分析报告</view>
        <view class="report-subtitle">Professional English Assessment Report</view>
      </view>
      
      <!-- 成绩统计 -->
      <view class="summary-section-top">
        <view class="summary-title">
          <text class="summary-icon">📈</text>
          <text class="summary-text">成绩统计</text>
        </view>
        <view class="summary-cards">
          <view class="summary-card total-card">
            <text class="card-number">{{totalSelectedScore}}</text>
            <text class="card-label">总分</text>
          </view>
          <view class="summary-card lost-card">
            <text class="card-number">{{totalLostScore}}</text>
            <text class="card-label">失分</text>
          </view>
          <view class="summary-card gained-card">
            <text class="card-number">{{totalGainedScore}}</text>
            <text class="card-label">得分</text>
          </view>
          <view class="summary-card rate-card">
            <text class="card-number">{{correctRate}}%</text>
            <text class="card-label">正确率</text>
          </view>
        </view>
      </view>
      
      <!-- 各题型得分率柱状图 -->
      <view class="chart-section">
        <view class="chart-title">
          <text class="chart-icon">📊</text>
          <text class="chart-text">各题型得分率</text>
          <text class="chart-tip" wx:if="{{chartData.length > 6}}">（可左右滑动查看）</text>
        </view>
        <view class="chart-container">
          <view class="chart-wrapper">
            <!-- Y轴标签 -->
            <view class="y-axis">
              <view class="y-label">100%</view>
              <view class="y-label">80%</view>
              <view class="y-label">60%</view>
              <view class="y-label">40%</view>
              <view class="y-label">20%</view>
              <view class="y-label">0%</view>
            </view>
            <!-- 柱状图主体 -->
            <view class="chart-bars">
              <view 
                class="bar-item"
                wx:for="{{chartData}}"
                wx:key="id"
              >
                <view class="bar-wrapper">
                  <view 
                    class="bar-fill"
                    style="height: {{item.rate}}%; background: linear-gradient(180deg, {{styles[selectedStyle].primary}}, {{styles[selectedStyle].secondary}});"
                  ></view>
                  <view class="bar-value">{{item.rate}}%</view>
                </view>
                <view class="bar-label">{{item.name}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 分析表格 -->
      <view class="analysis-table">
        <view class="table-title">
          <text class="title-icon">📋</text>
          <text class="title-text">详细分析表</text>
        </view>
        
        <!-- 表格头部 -->
        <view class="table-header" style="background: {{styles[selectedStyle].primary}};">
          <view class="th th-subject">题型</view>
          <view class="th th-total">总分</view>
          <view class="th th-lost">扣分</view>
          <view class="th th-reasons">原因分析</view>
          <view class="th th-suggestions">学习建议</view>
        </view>
        
        <!-- 表格内容 -->
        <view class="table-body">
          <view 
            class="table-row {{index % 2 === 0 ? 'even' : 'odd'}}"
            wx:for="{{reportData.sections}}"
            wx:key="id"
          >
            <view class="td td-subject">
              <view class="subject-name">{{item.name}}</view>
              <view class="subject-score">{{item.totalScore - (item.lostScore || 0)}}/{{item.totalScore}}</view>
            </view>
            <view class="td td-total">
              <text class="score-number total">{{item.totalScore}}</text>
            </view>
            <view class="td td-lost">
              <text class="score-number lost">{{item.lostScore || 0}}</text>
            </view>
            <view class="td td-reasons">
              <view class="content-list">
                <text 
                  class="list-item"
                  wx:for="{{item.selectedReasons}}"
                  wx:key="*this"
                  wx:for-item="reason"
                >• {{reason}}</text>
                <text 
                  class="list-item custom"
                  wx:if="{{item.customReason}}"
                >• {{item.customReason}}</text>
              </view>
            </view>
            <view class="td td-suggestions">
              <view class="content-list">
                <text 
                  class="list-item"
                  wx:for="{{item.selectedSuggestions}}"
                  wx:key="*this"
                  wx:for-item="suggestion"
                >• {{suggestion}}</text>
                <text 
                  class="list-item custom"
                  wx:if="{{item.customSuggestion}}"
                >• {{item.customSuggestion}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 下阶段课程规划表 -->
      <view class="analysis-table" wx:if="{{reportConfig.showCourseplan}}">
        <view class="table-title">
          <text class="title-icon">📅</text>
          <text class="title-text">课程规划</text>
        </view>
        
        <!-- 表格头部 -->
        <view class="table-header" style="background: {{styles[selectedStyle].primary}};">
          <view class="th th-lesson" style="width: 120rpx;">
            <input 
              class="table-header-input" 
              placeholder="课次" 
              value="{{coursePlanHeaders.lesson}}"
              data-field="lesson"
              bindinput="onCoursePlanHeaderChange"
            />
          </view>
          <view class="th th-content-plan" style="flex: 1;">
            <input 
              class="table-header-input" 
              placeholder="学习内容" 
              value="{{coursePlanHeaders.content}}"
              data-field="content"
              bindinput="onCoursePlanHeaderChange"
            />
          </view>
          <view class="th th-actions" style="width: 80rpx;">操作</view>
        </view>
        
        <!-- 表格内容 -->
        <view class="table-body">
          <view 
            class="table-row {{index % 2 === 0 ? 'even' : 'odd'}}"
            wx:for="{{coursePlanData}}"
            wx:key="id"
          >
            <view class="td td-lesson" style="width: 120rpx;">
              <text class="lesson-text">第{{item.lesson}}讲</text>
            </view>
            <view class="td td-content-plan" style="flex: 1;">
              <input 
                class="table-input" 
                placeholder="请输入学习内容" 
                value="{{item.content}}"
                data-index="{{index}}"
                bindinput="onCoursePlanContentChange"
              />
            </view>
            <view class="td td-actions" style="width: 80rpx;">
              <button 
                class="action-btn delete-btn" 
                data-index="{{index}}"
                bindtap="removeCoursePlanRow"
              >删除</button>
            </view>
          </view>
        </view>
        
        <!-- 添加行按钮 -->
        <view class="table-actions">
          <button class="add-row-btn" bindtap="addCoursePlanRow">
            <text class="add-icon">+</text>
            <text>添加课程</text>
          </button>
        </view>
      </view>
      
      <!-- 报告底部 -->
      <view class="report-footer">
        <view class="footer-decoration">
          <view class="decoration-dot"></view>
          <view class="decoration-dot"></view>
          <view class="decoration-dot"></view>
        </view>
        <view class="footer-info-centered">
          <text class="footer-text">生成时间：{{reportData.generatedAt}}{{basicInfo.analystName ? '　分析人：' + basicInfo.analystName : ''}}</text>
        </view>
        <view class="footer-signature">Powered by AI Technology</view>
      </view>
    </view>
    
    <view class="report-loading" wx:else>
      <text>正在生成报告...</text>
    </view>
  </view>
  </view>

  <!-- 进度指示器 -->
  <view class="progress-indicator">
    <view class="progress-step {{currentStep >= 0 ? 'completed' : ''}}">
      <view class="step-dot">1</view>
      <text class="step-text">基本信息</text>
    </view>
    <view class="progress-line {{currentStep >= 1 ? 'completed' : ''}}"></view>
    <view class="progress-step {{currentStep >= 1 ? 'completed' : ''}}">
      <view class="step-dot">2</view>
      <text class="step-text">选择题型</text>
    </view>
    <view class="progress-line {{currentStep >= 2 ? 'completed' : ''}}"></view>
    <view class="progress-step {{currentStep >= 2 ? 'completed' : ''}}">
      <view class="step-dot">3</view>
      <text class="step-text">题型分析</text>
    </view>
    <view class="progress-line {{currentStep >= 3 ? 'completed' : ''}}"></view>
    <view class="progress-step {{currentStep >= 3 ? 'completed' : ''}}">
      <view class="step-dot">3.5</view>
      <text class="step-text">课程规划</text>
    </view>
    <view class="progress-line {{currentStep >= 4 ? 'completed' : ''}}"></view>
    <view class="progress-step {{currentStep >= 4 ? 'completed' : ''}}">
      <view class="step-dot">4</view>
      <text class="step-text">试卷分析报告</text>
    </view>
  </view>
</view>

