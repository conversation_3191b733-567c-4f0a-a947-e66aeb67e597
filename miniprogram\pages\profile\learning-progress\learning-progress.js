Page({

  /**
   * 页面的初始数据
   */
  data: {
    progressList: [],
    loading: true,
    empty: false,
    editMode: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadLearningProgress();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 重新加载进度，以防有新的学习记录
    this.loadLearningProgress();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadLearningProgress();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 我的学习进度',
      path: '/pages/index/index'
    }
  },

  // 切换编辑模式
  toggleEditMode() {
    const { progressList } = this.data;
    
    // 退出编辑模式时重置所有选中状态
    const updatedList = progressList.map(item => ({
      ...item,
      isSelected: false
    }));
    
    this.setData({
      editMode: !this.data.editMode,
      progressList: updatedList
    });
  },

  // 全选/取消全选
  toggleSelectAll() {
    const { progressList } = this.data;
    const allSelected = progressList.every(item => item.isSelected);
    
    const updatedList = progressList.map(item => ({
      ...item,
      isSelected: !allSelected
    }));
    
    this.setData({
      progressList: updatedList
    });
  },

  // 切换单项选择
  toggleItemSelect(e) {
    const { key } = e.currentTarget.dataset;
    const { progressList } = this.data;
    
    const updatedList = progressList.map(item => {
      if (item.key === key) {
        return {
          ...item,
          isSelected: !item.isSelected
        };
      }
      return item;
    });
    
    this.setData({
      progressList: updatedList
    });
  },

  // 批量删除
  batchDelete() {
    const { progressList } = this.data;
    const selectedItems = progressList.filter(item => item.isSelected);
    const selectedKeys = selectedItems.map(item => item.key);
    
    if (selectedKeys.length === 0) {
      wx.showToast({
        title: '请选择要删除的进度',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedKeys.length} 个学习进度吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteProgressItems(selectedKeys);
        }
      }
    });
  },

  // 删除指定的进度项
  deleteProgressItems(keys) {
    try {
      const { progressList } = this.data;
      let deletedCount = 0;
      
      // 获取当前已删除的项目列表
      const deletedItems = wx.getStorageSync('deleted_progress_items') || [];
      
      // 删除本地存储中的真实数据并记录删除的项目
      keys.forEach(key => {
        if (!key.startsWith('demo_')) {
          // 真实的学习进度数据
          try {
            wx.removeStorageSync(key);
            deletedCount++;
          } catch (error) {
            console.log('删除存储数据失败:', key, error);
          }
        } else {
          // 演示数据，记录到已删除列表
          if (!deletedItems.includes(key)) {
            deletedItems.push(key);
          }
          deletedCount++;
        }
      });
      
      // 保存已删除的项目列表到本地存储
      try {
        wx.setStorageSync('deleted_progress_items', deletedItems);
        console.log('已更新删除记录:', deletedItems);
      } catch (error) {
        console.error('保存删除记录失败:', error);
      }
      
      // 从当前列表中移除已删除的项目
      const updatedList = progressList.filter(item => !keys.includes(item.key));
      
      // 如果删除后列表为空，设置空状态
      if (updatedList.length === 0) {
        this.setData({
          progressList: [],
          empty: true,
          editMode: false
        });
      } else {
        this.setData({
          progressList: updatedList,
          editMode: false
        });
      }
      
      wx.showToast({
        title: `删除成功 (${deletedCount}个)`,
        icon: 'success'
      });
      
    } catch (error) {
      console.error('删除进度失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  },

  // 加载学习进度
  loadLearningProgress() {
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 获取所有的学习进度
      const progressData = this.getAllProgress();
      
      if (progressData.length === 0) {
        this.setData({
          progressList: [],
          loading: false,
          empty: true
        });
      } else {
        // 为每个项目添加选中状态
        const processedData = progressData.map(item => ({
          ...item,
          isSelected: false
        }));
        
        this.setData({
          progressList: processedData,
          loading: false,
          empty: false
        });
      }

      wx.hideLoading();
    } catch (error) {
      console.error('加载学习进度失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 获取所有学习进度
  getAllProgress() {
    const progressList = [];
    
    try {
      // 获取已删除的项目列表
      const deletedItems = wx.getStorageSync('deleted_progress_items') || [];
      
      // 获取所有storage中的progress_开头的数据
      const { keys } = wx.getStorageInfoSync();
      const progressKeys = keys.filter(key => key.startsWith('progress_'));
      
      progressKeys.forEach(key => {
        try {
          const progressData = wx.getStorageSync(key);
          if (progressData && progressData.libraryId) {
            // 解析key获取libraryId和mode
            // 优先使用progressData中存储的libraryId，如果没有则从key解析
            let libraryId = progressData.libraryId;
            let mode = progressData.mode || 'default';

            // 如果progressData中没有libraryId，则从key解析
            if (!libraryId) {
              const keyParts = key.replace('progress_', '').split('_');
              // 对于复合libraryId（如gaokao_3500），需要特殊处理
              if (keyParts.length >= 3) {
                // 假设最后一个或两个部分是mode（如en_to_cn）
                if (keyParts[keyParts.length - 3] === 'to') {
                  // 格式：progress_gaokao_3500_en_to_cn
                  libraryId = keyParts.slice(0, -3).join('_');
                  mode = keyParts.slice(-3).join('_');
                } else if (keyParts[keyParts.length - 2] === 'to') {
                  // 格式：progress_gaokao_3500_cn_to_en
                  libraryId = keyParts.slice(0, -3).join('_');
                  mode = keyParts.slice(-3).join('_');
                } else {
                  // 格式：progress_gaokao_3500_elimination
                  libraryId = keyParts.slice(0, -1).join('_');
                  mode = keyParts[keyParts.length - 1];
                }
              } else if (keyParts.length === 2) {
                // 格式：progress_libraryId_mode
                libraryId = keyParts[0];
                mode = keyParts[1];
              } else {
                // 格式：progress_libraryId
                libraryId = keyParts[0];
                mode = 'default';
              }
            }
            
            // 处理分组进度显示
            let displayData = {
              ...progressData,
              libraryId: libraryId,
              mode: mode,
              key: key,
              modeText: this.getModeText(mode),
              lastStudyTimeText: this.formatTime(progressData.lastStudyTime),
              isDemo: false // 标记为真实数据
            };

            // 如果是分组学习，优化显示信息
            if (progressData.isGrouped && progressData.groupInfo) {
              const groupInfo = progressData.groupInfo;
              displayData.progressText = progressData.progressText || `第${groupInfo.currentGroup}/${groupInfo.totalGroups}组`;
              displayData.detailText = progressData.detailText || `已完成${groupInfo.completedGroups.length}组，共${groupInfo.totalGroups}组`;
              displayData.isGrouped = true;
              displayData.groupProgress = groupInfo.groupProgress || Math.round((groupInfo.completedGroups.length / groupInfo.totalGroups) * 100);
            }

            progressList.push(displayData);
          }
        } catch (error) {
          console.error('解析进度数据失败:', key, error);
        }
      });
      
      // 如果没有真实进度数据，添加默认的示例进度（排除已删除的）
      if (progressList.length === 0) {
        const defaultData = this.getDefaultProgressData();
        const filteredDefaultData = defaultData.filter(item => !deletedItems.includes(item.key));
        progressList.push(...filteredDefaultData);
      }
      
      // 按最后学习时间排序
      progressList.sort((a, b) => b.lastStudyTime - a.lastStudyTime);
      
      console.log('获取到的学习进度:', progressList);
      console.log('已删除的项目:', deletedItems);
      return progressList;
    } catch (error) {
      console.error('获取学习进度失败:', error);
      return this.getDefaultProgressData();
    }
  },

  // 获取默认进度数据（用于演示）
  getDefaultProgressData() {
    return [
      {
        libraryId: 'gaokao_3500',
        libraryName: '高考大纲词汇',
        mode: 'en_to_cn',
        modeText: '英译汉',
        currentIndex: 0,
        totalCount: 3500,
        percentage: 0,
        lastStudyTime: Date.now() - 86400000, // 1天前
        lastStudyTimeText: '1天前',
        key: 'demo_progress_1',
        isDemo: true,
        isSelected: false
      },
      {
        libraryId: 'gaokao_3500',
        libraryName: '高考大纲词汇',
        mode: 'cn_to_en',
        modeText: '汉译英',
        currentIndex: 0,
        totalCount: 3500,
        percentage: 0,
        lastStudyTime: Date.now() - 172800000, // 2天前
        lastStudyTimeText: '2天前',
        key: 'demo_progress_2',
        isDemo: true,
        isSelected: false
      },
      {
        libraryId: 'cet4',
        libraryName: '四级词汇',
        mode: 'dictation',
        modeText: '听写',
        currentIndex: 0,
        totalCount: 4428,
        percentage: 0,
        lastStudyTime: Date.now() - 259200000, // 3天前
        lastStudyTimeText: '3天前',
        key: 'demo_progress_3',
        isDemo: true,
        isSelected: false
      }
    ];
  },

  // 获取模式文本
  getModeText(mode) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写',
      'elimination': '消消乐',
      'default': '默认模式'
    };
    return modeMap[mode] || mode;
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '未知时间';
    
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  // 继续学习
  continueStudy(e) {
    const { item } = e.currentTarget.dataset;
    
    // 如果是演示数据，提示用户开始学习
    if (item.isDemo) {
      wx.showModal({
        title: '开始学习',
        content: `您还没有开始学习「${item.libraryName}」的「${item.modeText}」模式，现在开始吗？`,
        confirmText: '开始学习',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/wordbank/wordlist/wordlist?libraryId=${item.libraryId}`
            });
          }
        }
      });
      return;
    }

    // 继续学习实际的进度
    let url = `/pages/wordbank/wordlist/wordlist?libraryId=${item.libraryId}&mode=continue&targetMode=${item.mode}`;

    // 如果是分组学习，传递分组信息
    if (item.isGrouped && item.groupInfo) {
      url += `&isGrouped=true&currentGroup=${item.groupInfo.currentGroup}&totalGroups=${item.groupInfo.totalGroups}&wordsPerGroup=${item.groupInfo.wordsPerGroup}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  // 清除单个进度
  clearProgress(e) {
    const { item } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认清除',
      content: `确定要清除「${item.libraryName}」的「${item.modeText}」学习进度吗？此操作不可恢复。`,
      confirmText: '清除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteProgressItems([item.key]);
        }
      }
    });
  },

  // 查看词库
  viewLibrary(e) {
    const { item } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/wordbank/wordlist/wordlist?libraryId=${item.libraryId}`
    });
  },

  // 去学习页面
  goToWordbank() {
    wx.navigateTo({
      url: '/pages/wordbank/wordbank'
    });
  },

  // 重置删除记录（开发测试用）
  resetDeletedItems() {
    wx.showModal({
      title: '重置确认',
      content: '确定要重置所有删除记录吗？这将恢复所有已删除的演示数据。',
      confirmText: '重置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('deleted_progress_items');
            wx.showToast({
              title: '重置成功',
              icon: 'success'
            });
            // 重新加载进度数据
            this.loadLearningProgress();
          } catch (error) {
            console.error('重置失败:', error);
            wx.showToast({
              title: '重置失败',
              icon: 'error'
            });
          }
        }
      }
    });
  }
}); 