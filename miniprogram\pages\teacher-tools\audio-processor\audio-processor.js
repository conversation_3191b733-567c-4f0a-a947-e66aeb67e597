Page({
  data: {
    currentStep: 'upload', // upload, process, preview, complete
    
    // 上传的文件
    audioFile: null,
    wordFile: null,
    
    // 处理进度
    processProgress: 0,
    processStatus: '准备中...',
    
    // 分割结果
    audioSegments: [],
    questionData: [],
    
    // 预览相关
    currentPreviewIndex: 0,
    isPlaying: false,
    audioContext: null,
    
    // 题型配置
    questionTypes: [
      {
        id: 'listen_choose',
        name: '听后选择',
        description: '14道小题，每题播放2遍',
        segmentCount: 14,
        playTimes: 2
      },
      {
        id: 'listen_record',
        name: '听后记录与转述',
        description: '1道题，分为填空和转述两部分',
        segmentCount: 1,
        playTimes: 3
      },
      {
        id: 'read_answer',
        name: '朗读并回答问题',
        description: '1道题，包含朗读文本和2个问题',
        segmentCount: 1,
        playTimes: 1
      }
    ],
    selectedQuestionType: 'listen_choose',
    selectedQuestionTypeIndex: 0,
    selectedQuestionTypeName: '听后选择',
    selectedQuestionTypeDescription: '14道小题，每题播放2遍',

    // 分割参数
    silenceThreshold: 0.1, // 静音阈值
    minSegmentLength: 2, // 最小片段长度（秒）
    maxSegmentLength: 60, // 最大片段长度（秒）

    // 格式化显示的数值
    silenceThresholdDisplay: '0.10',
    minSegmentLengthDisplay: '2',
    maxSegmentLengthDisplay: '60',
    
    // 处理状态
    processing: false,
    uploadProgress: 0
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '听口音频处理'
    });
    
    // 初始化音频上下文
    this.setData({
      audioContext: wx.createInnerAudioContext()
    });
  },

  onUnload() {
    // 清理音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }
  },

  // 选择音频文件
  chooseAudioFile() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['audio'],
      sourceType: ['album'],
      success: (res) => {
        const audioFile = res.tempFiles[0];
        
        // 验证文件格式
        if (!audioFile.tempFilePath.toLowerCase().includes('.mp3') && 
            !audioFile.tempFilePath.toLowerCase().includes('.wav') &&
            !audioFile.tempFilePath.toLowerCase().includes('.m4a')) {
          wx.showToast({
            title: '请选择MP3、WAV或M4A格式的音频文件',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        
        // 验证文件大小（限制50MB）
        if (audioFile.size > 50 * 1024 * 1024) {
          wx.showToast({
            title: '音频文件不能超过50MB',
            icon: 'none'
          });
          return;
        }
        
        this.setData({
          audioFile: {
            ...audioFile,
            name: `音频文件_${new Date().getTime()}.mp3`,
            duration: 0, // 将在上传后获取
            sizeText: (audioFile.size / 1024 / 1024).toFixed(2) + 'MB'
          }
        });
        
        wx.showToast({
          title: '音频文件选择成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('选择音频文件失败:', err);
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        });
      }
    });
  },

  // 选择Word文档
  chooseWordFile() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['doc', 'docx'],
      success: (res) => {
        const wordFile = res.tempFiles[0];
        
        // 验证文件大小（限制10MB）
        if (wordFile.size > 10 * 1024 * 1024) {
          wx.showToast({
            title: 'Word文档不能超过10MB',
            icon: 'none'
          });
          return;
        }
        
        this.setData({
          wordFile: {
            ...wordFile,
            name: wordFile.name || `题目文档_${new Date().getTime()}.docx`,
            sizeText: (wordFile.size / 1024 / 1024).toFixed(2) + 'MB'
          }
        });
        
        wx.showToast({
          title: 'Word文档选择成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('选择Word文档失败:', err);
        wx.showToast({
          title: '选择文档失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 选择题型
  onQuestionTypeChange(e) {
    const selectedIndex = parseInt(e.detail.value);
    const selectedType = this.data.questionTypes[selectedIndex];

    this.setData({
      selectedQuestionType: selectedType.id,
      selectedQuestionTypeIndex: selectedIndex,
      selectedQuestionTypeName: selectedType.name,
      selectedQuestionTypeDescription: selectedType.description
    });

    wx.showToast({
      title: `已选择：${selectedType.name}`,
      icon: 'none'
    });
  },

  // 调整分割参数
  onParameterChange(e) {
    const { parameter } = e.currentTarget.dataset;
    let value = parseFloat(e.detail.value);

    // 根据参数类型格式化数值
    let displayValue;
    if (parameter === 'silenceThreshold') {
      value = Math.round(value * 100) / 100; // 保留2位小数
      displayValue = value.toFixed(2);
    } else {
      value = Math.round(value); // 整数
      displayValue = value.toString();
    }

    console.log(`参数 ${parameter} 变更为: ${value}`);

    this.setData({
      [parameter]: value,
      [`${parameter}Display`]: displayValue
    });
  },

  // 滑动控件滑动中事件
  onParameterChanging(e) {
    const { parameter } = e.currentTarget.dataset;
    let value = parseFloat(e.detail.value);

    // 根据参数类型格式化数值
    let displayValue;
    if (parameter === 'silenceThreshold') {
      value = Math.round(value * 100) / 100; // 保留2位小数
      displayValue = value.toFixed(2);
    } else {
      value = Math.round(value); // 整数
      displayValue = value.toString();
    }

    this.setData({
      [parameter]: value,
      [`${parameter}Display`]: displayValue
    });
  },

  // 开始处理
  async startProcessing() {
    const { audioFile, wordFile, selectedQuestionType } = this.data;

    // 验证必要文件
    if (!audioFile) {
      wx.showToast({
        title: '请先选择音频文件',
        icon: 'none'
      });
      return;
    }

    if (!wordFile) {
      wx.showToast({
        title: '请先选择Word文档',
        icon: 'none'
      });
      return;
    }

    this.setData({
      processing: true,
      currentStep: 'process',
      processProgress: 0,
      processStatus: '准备处理音频...'
    });

    try {
      // 简化处理：直接模拟音频分割
      await this.simulateAudioProcessing();

      // 完成处理
      this.setData({
        currentStep: 'preview',
        processing: false,
        processStatus: '处理完成'
      });

      wx.showToast({
        title: '处理完成！',
        icon: 'success'
      });

    } catch (error) {
      console.error('处理失败:', error);
      this.setData({
        processing: false,
        processStatus: '处理失败'
      });

      wx.showModal({
        title: '处理失败',
        content: error.message || '音频处理过程中出现错误，请重试',
        showCancel: false
      });
    }
  },

  // 模拟音频处理（简化版本）
  async simulateAudioProcessing() {
    const { selectedQuestionType, audioFile } = this.data;
    const questionTypeConfig = this.data.questionTypes.find(type => type.id === selectedQuestionType);

    // 模拟处理进度
    for (let i = 0; i <= 100; i += 10) {
      this.setData({
        processProgress: i,
        processStatus: `处理中... ${i}%`
      });
      await this.delay(200);
    }

    // 生成模拟的音频片段
    const segments = [];
    for (let i = 0; i < questionTypeConfig.segmentCount; i++) {
      segments.push({
        id: i + 1,
        title: `第${i + 1}题`,
        duration: Math.floor(Math.random() * 30) + 10, // 10-40秒随机时长
        audioUrl: audioFile.tempFilePath, // 使用原音频作为示例
        startTime: i * 30,
        endTime: (i + 1) * 30,
        playTimes: questionTypeConfig.playTimes
      });
    }

    this.setData({
      audioSegments: segments,
      processProgress: 100,
      processStatus: '处理完成'
    });
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // 上传音频文件
  async uploadAudioFile() {
    return new Promise((resolve, reject) => {
      const { audioFile } = this.data;
      
      this.setData({
        processStatus: '上传音频文件中...',
        processProgress: 10
      });
      
      wx.cloud.uploadFile({
        cloudPath: `audio_processing/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.mp3`,
        filePath: audioFile.tempFilePath,
        success: (res) => {
          this.setData({
            'audioFile.cloudFileID': res.fileID,
            processProgress: 30
          });
          resolve(res);
        },
        fail: (err) => {
          reject(new Error('音频文件上传失败'));
        }
      });
    });
  },

  // 上传Word文档
  async uploadWordFile() {
    return new Promise((resolve, reject) => {
      const { wordFile } = this.data;
      
      this.setData({
        processStatus: '上传Word文档中...',
        processProgress: 40
      });
      
      wx.cloud.uploadFile({
        cloudPath: `audio_processing/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.docx`,
        filePath: wordFile.path,
        success: (res) => {
          this.setData({
            'wordFile.cloudFileID': res.fileID,
            processProgress: 60
          });
          resolve(res);
        },
        fail: (err) => {
          reject(new Error('Word文档上传失败'));
        }
      });
    });
  },

  // 处理文件
  async processFiles() {
    return new Promise((resolve, reject) => {
      const { audioFile, wordFile, selectedQuestionType, silenceThreshold, minSegmentLength, maxSegmentLength } = this.data;
      
      this.setData({
        processStatus: '智能分析音频中...',
        processProgress: 70
      });
      
      wx.cloud.callFunction({
        name: 'processListeningAudio',
        data: {
          audioFileID: audioFile.cloudFileID,
          wordFileID: wordFile.cloudFileID,
          questionType: selectedQuestionType,
          parameters: {
            silenceThreshold,
            minSegmentLength,
            maxSegmentLength
          }
        },
        config: {
          timeout: 300000 // 5分钟超时
        },
        success: (res) => {
          if (res.result && res.result.success) {
            this.setData({
              audioSegments: res.result.data.audioSegments || [],
              questionData: res.result.data.questionData || [],
              processProgress: 100
            });
            resolve(res.result);
          } else {
            reject(new Error(res.result?.message || '音频处理失败'));
          }
        },
        fail: (err) => {
          console.error('云函数调用失败:', err);
          reject(new Error('音频处理服务暂时不可用，请稍后重试'));
        }
      });
    });
  },

  // 预览音频片段
  previewSegment(e) {
    const index = e.currentTarget.dataset.index;
    const segment = this.data.audioSegments[index];
    
    if (!segment || !segment.audioUrl) {
      wx.showToast({
        title: '音频片段不可用',
        icon: 'none'
      });
      return;
    }
    
    const { audioContext } = this.data;
    
    // 停止当前播放
    audioContext.stop();
    
    // 播放新片段
    audioContext.src = segment.audioUrl;
    audioContext.play();
    
    this.setData({
      currentPreviewIndex: index,
      isPlaying: true
    });
    
    // 监听播放结束
    audioContext.onEnded(() => {
      this.setData({
        isPlaying: false
      });
    });
  },

  // 停止播放
  stopPreview() {
    const { audioContext } = this.data;
    audioContext.stop();
    
    this.setData({
      isPlaying: false
    });
  },

  // 保存到听口训练
  async saveToListening() {
    const { audioSegments, questionData, selectedQuestionType } = this.data;
    
    if (audioSegments.length === 0 || questionData.length === 0) {
      wx.showToast({
        title: '没有可保存的数据',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'saveListeningQuestions',
        data: {
          questionType: selectedQuestionType,
          audioSegments: audioSegments,
          questionData: questionData
        }
      });
      
      if (result.result && result.result.success) {
        wx.hideLoading();
        
        this.setData({
          currentStep: 'complete'
        });
        
        wx.showModal({
          title: '保存成功',
          content: `已成功保存${audioSegments.length}个音频片段和${questionData.length}道题目到听口训练模块`,
          showCancel: false,
          confirmText: '查看听口训练',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/listening/listening'
              });
            }
          }
        });
      } else {
        throw new Error(result.result?.message || '保存失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存失败:', error);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  // 重新开始
  restart() {
    this.setData({
      currentStep: 'upload',
      audioFile: null,
      wordFile: null,
      audioSegments: [],
      questionData: [],
      processProgress: 0,
      processStatus: '准备中...',
      processing: false,
      currentPreviewIndex: 0,
      isPlaying: false
    });
    
    // 停止音频播放
    if (this.data.audioContext) {
      this.data.audioContext.stop();
    }
  }
});
