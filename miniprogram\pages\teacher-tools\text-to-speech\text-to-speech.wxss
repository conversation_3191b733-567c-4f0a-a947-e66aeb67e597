.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 通用区块样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #007aff;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.text-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background-color: #fafafa;
}

.text-counter {
  text-align: right;
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 设置区域 */
.settings-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.setting-item {
  margin-bottom: 40rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.setting-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  background-color: #fafafa;
  font-size: 28rpx;
}

.picker-arrow {
  color: #666;
  font-size: 24rpx;
}

.setting-slider {
  width: 100%;
  margin-top: 10rpx;
}

/* 操作按钮区域 */
.action-section {
  margin-bottom: 30rpx;
}

.action-buttons-row {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  min-width: 0;
  white-space: nowrap; /* 防止文字换行 */
  padding: 0 10rpx; /* 添加左右内边距 */
}

.action-btn.primary {
  background: linear-gradient(135deg, #007aff 0%, #0056b3 100%);
  color: white;
}

.action-btn.secondary {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
}

.action-btn.success {
  background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
  color: white;
}

.action-btn.loading {
  opacity: 0.7;
}

.action-btn[disabled] {
  background: #e9ecef !important;
  color: #6c757d !important;
  box-shadow: none;
}

/* 小屏幕适配 */
@media screen and (max-width: 400px) {
  .action-buttons-row {
    gap: 10rpx;
  }

  .action-btn {
    font-size: 22rpx;
    height: 76rpx;
    padding: 0 8rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 350px) {
  .action-btn {
    font-size: 20rpx;
    height: 72rpx;
    padding: 0 6rpx;
  }
}



/* 使用说明区域 */
.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.tips-list {
  margin-top: 20rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
  flex: 1;
}
